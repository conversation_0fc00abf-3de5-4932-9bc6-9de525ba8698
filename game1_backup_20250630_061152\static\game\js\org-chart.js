// Rwenzori Innovations Organization Chart
// This file defines the organization structure and handles visualization

// Organization structure based on the provided image
const organizationStructure = {
    // Top level
    shareholders: {
        title: "Shareholders",
        level: 6,
        children: ["coo"]
    },

    // Executive level
    coo: {
        title: "Chief Operating Officer (COO)",
        level: 5,
        parent: "shareholders",
        children: ["vp_marketing", "vp_operations", "vp_finance"]
    },

    // Vice President level
    vp_marketing: {
        title: "Vice-President/Marketing",
        level: 4,
        parent: "coo",
        children: ["sales_manager", "advertising_manager"]
    },
    vp_operations: {
        title: "Vice-President/Operations",
        level: 4,
        parent: "coo",
        children: ["production_manager", "service_manager", "facilities_manager"]
    },
    vp_finance: {
        title: "Vice-President/Finance",
        level: 4,
        parent: "coo",
        children: ["accounts_receivable_manager", "accounts_payable_manager"]
    },

    // Manager level - Marketing
    sales_manager: {
        title: "Sales Manager",
        level: 3,
        parent: "vp_marketing",
        children: ["sales_associate"]
    },
    advertising_manager: {
        title: "Advertising/Research Manager",
        level: 3,
        parent: "vp_marketing",
        children: ["marketing_associate"]
    },

    // Manager level - Operations
    production_manager: {
        title: "Production Manager",
        level: 3,
        parent: "vp_operations",
        children: ["production_associate"]
    },
    service_manager: {
        title: "Service Manager",
        level: 3,
        parent: "vp_operations",
        children: ["service_associate"]
    },
    facilities_manager: {
        title: "Facilities Manager",
        level: 3,
        parent: "vp_operations",
        children: ["facilities_associate"]
    },

    // Manager level - Finance
    accounts_receivable_manager: {
        title: "Accounts Receivable Manager",
        level: 3,
        parent: "vp_finance",
        children: ["accounts_receivable_associate"]
    },
    accounts_payable_manager: {
        title: "Accounts Payable Manager",
        level: 3,
        parent: "vp_finance",
        children: ["accounts_payable_associate"]
    },

    // Associate level - Marketing
    sales_associate: {
        title: "Sales Associate",
        level: 2,
        parent: "sales_manager"
    },
    marketing_associate: {
        title: "Marketing Associate",
        level: 2,
        parent: "advertising_manager"
    },

    // Associate level - Operations
    production_associate: {
        title: "Production Associate",
        level: 2,
        parent: "production_manager"
    },
    service_associate: {
        title: "Service Associate",
        level: 2,
        parent: "service_manager"
    },
    facilities_associate: {
        title: "Facilities Associate",
        level: 2,
        parent: "facilities_manager"
    },

    // Associate level - Finance
    accounts_receivable_associate: {
        title: "Accounts Receivable Associate",
        level: 2,
        parent: "accounts_receivable_manager"
    },
    accounts_payable_associate: {
        title: "Accounts Payable Associate",
        level: 2,
        parent: "accounts_payable_manager"
    },

    // Entry level - HR
    hr_associate: {
        title: "HR Associate",
        level: 1,
        parent: null
    },

    // Applicant level
    applicant: {
        title: "Applicant",
        level: 0,
        parent: null
    }
};

// Define the progression path - Aligned with GAME_ARCHITECTURE_GUIDE.md
// This follows the simplified 10-role progression path as specified in the architecture guide
const progressionPath = [
    "applicant",                    // 1 task (Entry Level Exception)
    "junior_assistant",             // 3 tasks
    "sales_associate",              // 3 tasks
    "marketing_associate",          // 3 tasks
    "senior_marketing_specialist",  // 3 tasks
    "marketing_manager",            // 3 tasks
    "senior_marketing_manager",     // 3 tasks
    "marketing_director",           // 3 tasks
    "vp_marketing",                 // 3 tasks
    "cmo"                          // 3 tasks (Final role)
];

// Tasks for each position - 3 tasks per position
const positionTasks = {
    // Entry level tasks
    applicant: [
        {
            id: "cover_letter",
            title: "Create a Cover Letter",
            description: "Write a compelling cover letter for your application to Rwenzori Innovations"
        },
        {
            id: "resume",
            title: "Update Your Resume",
            description: "Tailor your resume to highlight skills relevant to Rwenzori Innovations"
        },
        {
            id: "interview",
            title: "Complete the Interview",
            description: "Successfully answer interview questions to join Rwenzori Innovations"
        }
    ],

    hr_associate: [
        {
            id: "onboarding_checklist",
            title: "Create Onboarding Checklist",
            description: "Develop a comprehensive onboarding checklist for new employees."
        },
        {
            id: "employee_handbook",
            title: "Update Employee Handbook",
            description: "Update the remote work policy in the employee handbook."
        },
        {
            id: "training_program",
            title: "Design Training Program",
            description: "Create a training program for new hires in the HR department."
        }
    ],

    // Marketing Department
    marketing_associate: [
        {
            id: "market_research",
            title: "Conduct Market Research",
            description: "Research market trends for solar panel products and compile findings."
        },
        {
            id: "social_media",
            title: "Create Social Media Strategy",
            description: "Develop a social media strategy to promote solar panel products."
        },
        {
            id: "content_calendar",
            title: "Design Content Calendar",
            description: "Create a content calendar for upcoming marketing campaigns."
        }
    ],

    advertising_manager: [
        {
            id: "ad_campaign",
            title: "Design Ad Campaign",
            description: "Create an advertising campaign for a new solar panel product line."
        },
        {
            id: "market_analysis",
            title: "Perform Market Analysis",
            description: "Analyze market data to identify target demographics for solar panels."
        },
        {
            id: "brand_guidelines",
            title: "Update Brand Guidelines",
            description: "Revise the company's brand guidelines for consistency across platforms."
        }
    ],

    // Operations Department
    service_associate: [
        {
            id: "customer_service",
            title: "Improve Customer Service",
            description: "Develop strategies to enhance customer service for solar panel support."
        },
        {
            id: "service_metrics",
            title: "Track Service Metrics",
            description: "Create a system for tracking and improving service metrics."
        },
        {
            id: "feedback_system",
            title: "Implement Feedback System",
            description: "Design a system for collecting and acting on customer feedback."
        }
    ],

    production_associate: [
        {
            id: "production_efficiency",
            title: "Optimize Production",
            description: "Identify ways to improve efficiency in the solar panel production line."
        },
        {
            id: "quality_control",
            title: "Enhance Quality Control",
            description: "Develop better quality control processes for solar panel manufacturing."
        },
        {
            id: "inventory_management",
            title: "Streamline Inventory",
            description: "Create a system for more efficient inventory management."
        }
    ],

    facilities_associate: [
        {
            id: "workspace_optimization",
            title: "Optimize Workspace",
            description: "Redesign workspace layout for improved productivity."
        },
        {
            id: "maintenance_schedule",
            title: "Create Maintenance Schedule",
            description: "Develop a preventative maintenance schedule for facility equipment."
        },
        {
            id: "safety_protocols",
            title: "Update Safety Protocols",
            description: "Review and update workplace safety protocols."
        }
    ],

    service_manager: [
        {
            id: "service_improvement",
            title: "Service Improvement Plan",
            description: "Create a comprehensive plan to improve solar panel support services."
        },
        {
            id: "team_training",
            title: "Design Team Training",
            description: "Develop a training program for service team members."
        },
        {
            id: "service_automation",
            title: "Implement Service Automation",
            description: "Identify processes that can be automated to improve service efficiency."
        }
    ],

    production_manager: [
        {
            id: "production_planning",
            title: "Production Planning",
            description: "Create a production plan to meet increased solar panel demand."
        },
        {
            id: "process_improvement",
            title: "Process Improvement",
            description: "Identify and implement process improvements in solar panel manufacturing."
        },
        {
            id: "team_efficiency",
            title: "Team Efficiency",
            description: "Develop strategies to improve production team efficiency."
        }
    ],

    facilities_manager: [
        {
            id: "facility_expansion",
            title: "Facility Expansion Plan",
            description: "Create a plan for expanding the solar panel production facility."
        },
        {
            id: "energy_efficiency",
            title: "Energy Efficiency",
            description: "Identify ways to improve energy efficiency in the facility."
        },
        {
            id: "vendor_management",
            title: "Vendor Management",
            description: "Develop a system for managing facility maintenance vendors."
        }
    ],

    // Finance Department
    accounts_receivable_associate: [
        {
            id: "invoice_process",
            title: "Streamline Invoicing",
            description: "Improve the invoicing process for solar panel sales."
        },
        {
            id: "payment_tracking",
            title: "Enhance Payment Tracking",
            description: "Develop a better system for tracking customer payments."
        },
        {
            id: "collection_strategy",
            title: "Create Collection Strategy",
            description: "Design a strategy for collecting overdue payments."
        }
    ],

    accounts_payable_associate: [
        {
            id: "vendor_payments",
            title: "Optimize Vendor Payments",
            description: "Streamline the process for paying solar panel component vendors."
        },
        {
            id: "expense_reporting",
            title: "Improve Expense Reporting",
            description: "Create a more efficient expense reporting system."
        },
        {
            id: "payment_scheduling",
            title: "Develop Payment Scheduling",
            description: "Create a system for scheduling vendor payments to optimize cash flow."
        }
    ],

    accounts_receivable_manager: [
        {
            id: "collection_improvement",
            title: "Improve Collections",
            description: "Develop strategies to improve the collection of accounts receivable."
        },
        {
            id: "credit_policy",
            title: "Update Credit Policy",
            description: "Review and update the company's credit policy for solar panel customers."
        },
        {
            id: "revenue_forecasting",
            title: "Enhance Revenue Forecasting",
            description: "Improve methods for forecasting solar panel sales revenue."
        }
    ],

    accounts_payable_manager: [
        {
            id: "payment_optimization",
            title: "Optimize Payment Process",
            description: "Streamline the accounts payable process for better efficiency."
        },
        {
            id: "vendor_relationships",
            title: "Improve Vendor Relationships",
            description: "Develop strategies for better vendor relationship management."
        },
        {
            id: "cost_reduction",
            title: "Identify Cost Reductions",
            description: "Find opportunities to reduce costs in the accounts payable process."
        }
    ],

    // Vice President Level
    vp_operations: [
        {
            id: "operations_strategy",
            title: "Develop Operations Strategy",
            description: "Create a comprehensive operations strategy for solar panel production."
        },
        {
            id: "department_coordination",
            title: "Improve Department Coordination",
            description: "Develop methods to improve coordination between operations departments."
        },
        {
            id: "operational_efficiency",
            title: "Enhance Operational Efficiency",
            description: "Identify ways to improve overall operational efficiency."
        }
    ],

    vp_finance: [
        {
            id: "financial_planning",
            title: "Strategic Financial Planning",
            description: "Create a long-term financial plan for solar panel production and sales."
        },
        {
            id: "budget_optimization",
            title: "Optimize Departmental Budgets",
            description: "Review and optimize budgets across all departments."
        },
        {
            id: "investment_strategy",
            title: "Develop Investment Strategy",
            description: "Create an investment strategy for company growth."
        }
    ],

    // Executive Level
    coo: [
        {
            id: "operational_efficiency",
            title: "Improve Operational Efficiency",
            description: "Analyze and improve operational efficiency across all departments."
        },
        {
            id: "strategic_planning",
            title: "Strategic Planning",
            description: "Develop a strategic plan for company growth and expansion."
        },
        {
            id: "supply_chain_optimization",
            title: "Optimize Supply Chain",
            description: "Create a plan to optimize the solar panel supply chain."
        }
    ],

    shareholders: [
        {
            id: "annual_report",
            title: "Prepare Annual Report",
            description: "Create an annual report for shareholders detailing company performance."
        },
        {
            id: "growth_strategy",
            title: "Present Growth Strategy",
            description: "Present a comprehensive growth strategy to the board of directors."
        },
        {
            id: "succession_planning",
            title: "Develop Succession Plan",
            description: "Create a succession plan for key executive positions."
        }
    ]
};

// Game state tracking
let orgChartState = {
    currentPosition: "applicant",
    completedTasks: [],
    completedPositions: [],
    currentTaskIndex: 0
};

// Initialize the organization chart
function initOrgChart() {
    const orgChartContainer = document.getElementById('org-chart-container');
    if (!orgChartContainer) return;

    // Clear existing content
    orgChartContainer.innerHTML = '';

    // Create title
    const title = document.createElement('h2');
    title.textContent = 'Rwenzori Innovations Organization Chart';
    orgChartContainer.appendChild(title);

    // Create chart container
    const chartDiv = document.createElement('div');
    chartDiv.id = 'org-chart';
    chartDiv.className = 'org-chart';
    orgChartContainer.appendChild(chartDiv);

    // Create levels (from top to bottom)
    for (let level = 6; level >= 0; level--) {
        const levelDiv = document.createElement('div');
        levelDiv.className = 'org-level';
        levelDiv.setAttribute('data-level', level);

        // Find positions at this level
        const positionsAtLevel = Object.entries(organizationStructure)
            .filter(([_, position]) => position.level === level)
            .map(([id, position]) => ({ id, ...position }));

        // Create nodes for each position at this level
        positionsAtLevel.forEach(position => {
            const nodeDiv = document.createElement('div');
            nodeDiv.className = 'org-node';
            nodeDiv.id = `node-${position.id}`;
            nodeDiv.setAttribute('data-position', position.id);
            nodeDiv.textContent = position.title;

            // Add status class based on game state
            if (position.id === orgChartState.currentPosition) {
                nodeDiv.classList.add('current');
            } else if (orgChartState.completedPositions.includes(position.id)) {
                nodeDiv.classList.add('completed');
            } else {
                nodeDiv.classList.add('upcoming');
            }

            levelDiv.appendChild(nodeDiv);
        });

        chartDiv.appendChild(levelDiv);
    }

    // Add connecting lines (can be enhanced with SVG for better visualization)
    addConnectingLines();
}

// Add connecting lines between positions
function addConnectingLines() {
    // This is a placeholder for a more sophisticated implementation
    // In a real implementation, you might use SVG or canvas to draw lines
    console.log("Connecting lines would be added here");
}

// Update the organization chart based on current game state
function updateOrgChart() {
    // Reset all nodes
    document.querySelectorAll('.org-node').forEach(node => {
        node.classList.remove('current', 'completed', 'upcoming');
        node.classList.add('upcoming');
    });

    // Mark completed positions
    orgChartState.completedPositions.forEach(position => {
        const node = document.getElementById(`node-${position}`);
        if (node) {
            node.classList.remove('upcoming', 'current');
            node.classList.add('completed');
        }
    });

    // Mark current position
    const currentNode = document.getElementById(`node-${orgChartState.currentPosition}`);
    if (currentNode) {
        currentNode.classList.remove('upcoming', 'completed');
        currentNode.classList.add('current');
    }
}

// Get tasks for the current position
function getCurrentTasks() {
    return positionTasks[orgChartState.currentPosition] || [];
}

// Check if all tasks for the current position are completed
function areAllTasksCompleted() {
    const currentTasks = getCurrentTasks();
    return currentTasks.every(task =>
        orgChartState.completedTasks.includes(`${orgChartState.currentPosition}_${task.id}`)
    );
}

// Advance to the next position
function advanceToNextPosition() {
    // Add current position to completed positions
    if (!orgChartState.completedPositions.includes(orgChartState.currentPosition)) {
        orgChartState.completedPositions.push(orgChartState.currentPosition);
    }

    // Find current position index in progression path
    const currentIndex = progressionPath.indexOf(orgChartState.currentPosition);

    // If there's a next position, advance to it
    if (currentIndex < progressionPath.length - 1) {
        orgChartState.currentPosition = progressionPath[currentIndex + 1];
        orgChartState.currentTaskIndex = 0;

        // Update the organization chart
        updateOrgChart();

        return true;
    }

    // No more positions, game completed
    return false;
}

// Complete a task
function completeTask(taskId) {
    const fullTaskId = `${orgChartState.currentPosition}_${taskId}`;

    // Add to completed tasks if not already completed
    if (!orgChartState.completedTasks.includes(fullTaskId)) {
        orgChartState.completedTasks.push(fullTaskId);
    }

    // Check if all tasks for current position are completed
    if (areAllTasksCompleted()) {
        // Advance to next position
        const hasNextPosition = advanceToNextPosition();

        // If no more positions, game is completed
        if (!hasNextPosition) {
            // Handle game completion
            console.log("Game completed!");
        }
    } else {
        // Move to next task in current position
        orgChartState.currentTaskIndex = Math.min(
            orgChartState.currentTaskIndex + 1,
            getCurrentTasks().length - 1
        );
    }
}

// Get the current task
function getCurrentTask() {
    const tasks = getCurrentTasks();
    return tasks[orgChartState.currentTaskIndex] || null;
}

// Initialize the game state from saved data or defaults
function initGameState() {
    // Try to load from localStorage
    const savedState = localStorage.getItem('orgChartGameState');
    if (savedState) {
        try {
            orgChartState = JSON.parse(savedState);
        } catch (e) {
            console.error("Error loading saved game state:", e);
            // Use default state
        }
    }

    // Initialize the organization chart
    initOrgChart();
}

// Save the game state
function saveGameState() {
    localStorage.setItem('orgChartGameState', JSON.stringify(orgChartState));
}

// Reset the game
function resetGame() {
    orgChartState = {
        currentPosition: "applicant",
        completedTasks: [],
        completedPositions: [],
        currentTaskIndex: 0
    };

    saveGameState();
    initOrgChart();
}

// Export functions and data for use in other modules
window.OrgChart = {
    initGameState,
    updateOrgChart,
    getCurrentTasks,
    getCurrentTask,
    completeTask,
    resetGame,
    orgChartState,
    organizationStructure,
    progressionPath,
    positionTasks
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initGameState);
