# Generated manually to fix foreign key constraint

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0016_remove_certification_resume_remove_education_resume_and_more'),
        ('game', '0011_anonymousplayersettings'),
    ]

    operations = [
        # First, remove the old foreign key constraint
        migrations.RunSQL(
            "ALTER TABLE game_gamesession DROP CONSTRAINT IF EXISTS game_gamesession_company_id_845e0fae_fk_game_company_id;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        
        # Update any existing company_id values that reference game_company to NULL
        # since they're invalid references
        migrations.RunSQL(
            "UPDATE game_gamesession SET company_id = NULL WHERE company_id IS NOT NULL;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        
        # Remove the old company field
        migrations.RemoveField(
            model_name='gamesession',
            name='company',
        ),
        
        # Add the new company field with correct foreign key to corporate_company
        migrations.AddField(
            model_name='gamesession',
            name='company',
            field=models.ForeignKey(
                blank=True, 
                help_text='The company this game session belongs to', 
                null=True, 
                on_delete=django.db.models.deletion.CASCADE, 
                related_name='game_sessions', 
                to='corporate.company'
            ),
        ),
    ]
