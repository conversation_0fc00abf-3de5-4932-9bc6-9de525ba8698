/* Celebration styles */

/* Confetti containers */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999; /* Behind the overlay */
    overflow: hidden;
}

/* Front confetti container - appears in front of the overlay */
.confetti-container-front {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10001; /* Above the overlay (10000) */
    overflow: hidden;
}

/* Individual confetti piece */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    top: -20px;
    border-radius: 0;
    animation: confetti-fall linear forwards;
}

/* Add different shapes for confetti */
.confetti:nth-child(3n) {
    border-radius: 50%; /* Circle */
}

.confetti:nth-child(3n+1) {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* Triangle */
}

.confetti:nth-child(5n) {
    width: 8px;
    height: 15px; /* Rectangle */
}

/* Confetti in front container gets a subtle glow and different animation */
.confetti-container-front .confetti {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    animation-name: confetti-fall-front;
}

/* Front confetti animation with different timing */
@keyframes confetti-fall-front {
    0% {
        transform: translateY(0) rotate(0deg) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    30% {
        transform: translateY(30vh) rotate(180deg) translateX(-20px);
    }
    60% {
        transform: translateY(60vh) rotate(360deg) translateX(20px);
    }
    80% {
        transform: translateY(80vh) rotate(540deg) translateX(-10px);
        opacity: 0.8;
    }
    100% {
        transform: translateY(100vh) rotate(720deg) translateX(0);
        opacity: 0;
    }
}

/* Confetti fall animation with swaying */
@keyframes confetti-fall {
    0% {
        transform: translateY(0) rotate(0deg) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    25% {
        transform: translateY(25vh) rotate(180deg) translateX(15px);
    }
    50% {
        transform: translateY(50vh) rotate(360deg) translateX(-15px);
    }
    75% {
        transform: translateY(75vh) rotate(540deg) translateX(15px);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg) translateX(0);
        opacity: 0;
    }
}

/* Celebration overlay */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

/* Celebration content */
.celebration-content {
    background-color: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 80%;
    animation: celebration-appear 0.5s ease-out;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

/* Task completion specific styling */
.celebration-content.task-completion {
    background-color: #f5f9f7;
    border: 4px solid #4caf50;
    padding: 40px;
    min-width: 500px;
    max-width: 800px;
}

/* Promotion-specific styling */
.celebration-content.promotion {
    background-color: #f0f4ff;
    border: 5px solid #4a86e8;
    padding: 50px;
    box-shadow: 0 0 30px rgba(74, 134, 232, 0.5);
    min-width: 600px;
    max-width: 900px;
}

/* Celebration appear animation */
@keyframes celebration-appear {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    75% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Pulse animation for buttons */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
}

/* Celebration close button */
.celebration-close-button {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #4a86e8;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.celebration-close-button:hover {
    background-color: #3a76d8;
}

/* Celebration message */
.celebration-content h2 {
    color: #4a86e8;
    margin-bottom: 10px;
    font-size: 28px;
}

/* Task completion heading */
.celebration-content.task-completion h2 {
    color: #2e7d32;
    font-size: 28px;
    margin-bottom: 15px;
}

/* Promotion-specific heading */
.celebration-content.promotion h2 {
    color: #2a56b8;
    font-size: 32px;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.celebration-content p {
    font-size: 18px;
    margin-bottom: 20px;
    color: #333;
}

/* Task continuation message */
.continuation-message {
    font-style: italic;
    color: #555;
    background-color: #e8f5e9;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0;
    border-left: 3px solid #4caf50;
}

/* Promotion explanation text */
.promotion-explanation {
    font-style: italic;
    color: #555;
    background-color: #e3f2fd;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0;
    border-left: 3px solid #2196f3;
}

/* Career advancement text */
.career-advancement {
    font-weight: bold;
    color: #333;
    background-color: #e8eaf6;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0;
    text-align: center;
}

/* Promotion button */
.celebration-close-button.promotion-button {
    background-color: #3f51b5;
    font-weight: bold;
    padding: 15px 30px;
    font-size: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
    margin-top: 25px;
}

.celebration-close-button.promotion-button:hover {
    background-color: #303f9f;
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
}

/* Next task button removed - tasks now progress automatically */

/* Manual next task button removed - tasks now progress automatically */
