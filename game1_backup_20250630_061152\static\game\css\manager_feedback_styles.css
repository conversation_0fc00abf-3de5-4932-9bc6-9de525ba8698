/* Enhanced evaluation container */
.enhanced-evaluation-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
}

.enhanced-evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.meets-requirements-indicator {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9rem;
}

.meets-requirements-indicator.yes {
    background-color: #d4edda;
    color: #155724;
}

.meets-requirements-indicator.no {
    background-color: #f8d7da;
    color: #721c24;
}

.improvement-feedback {
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Manager feedback container */
.manager-feedback-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 5px;
    border: 1px solid #b8daff;
}

.manager-feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.manager-feedback-header h3 {
    margin: 0;
    color: #0056b3;
    font-size: 1.1rem;
}

.manager-feedback-label {
    font-weight: bold;
    color: #0056b3;
}

.manager-feedback-content {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #212529;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 4px;
    border-left: 4px solid #007bff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Highlight effect for task messages when retrying */
.highlight-message {
    animation: highlight-pulse 2s ease-in-out;
    border: 2px solid #4a86e8;
    box-shadow: 0 0 10px rgba(74, 134, 232, 0.5);
}

@keyframes highlight-pulse {
    0% { box-shadow: 0 0 5px rgba(74, 134, 232, 0.5); }
    50% { box-shadow: 0 0 15px rgba(74, 134, 232, 0.8); }
    100% { box-shadow: 0 0 5px rgba(74, 134, 232, 0.5); }
}

/* Resent task message styling */
.message.is-resent {
    position: relative;
    border-left: 4px solid #4a86e8;
    margin-top: 15px; /* Add space above for the label */
}

.message.is-resent::before {
    content: "RETRY THIS TASK";
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: #4a86e8;
    color: white;
    font-size: 12px;
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 4px;
    z-index: 1;
}

/* Ensure resent messages appear in the message container */
.messages-container .message.is-resent {
    display: flex;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    order: 999; /* Ensure it appears at the end of flex container */
}

/* Task failure modal styles */
.failure-feedback {
    padding: 15px;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 5px;
    border-left: 4px solid #dc3545;
    font-size: 0.95rem;
    line-height: 1.5;
}

.failure-score-info {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.failure-score-info p {
    margin: 5px 0;
    font-weight: bold;
}

.failure-score-info p:first-child {
    color: #dc3545;
}

.failure-score-info p:last-child {
    color: #0d6efd;
}
