"""
Evaluation functions for the Context-Aware Game.
This module contains functions for evaluating user prompts.
"""

import os
import logging
from .utils import log_info, log_error

# Check if prompt evaluation module is available
try:
    from prompt_evaluation import evaluate_prompt
    PROMPT_EVALUATION_AVAILABLE = True
    log_info("Prompt evaluation module loaded successfully")
except ImportError as e:
    log_error("Prompt evaluation module not available", e)
    PROMPT_EVALUATION_AVAILABLE = False

# Check if enhanced evaluation module is available
try:
    from enhanced_evaluation import evaluate_response_with_enhanced_llm, calculate_score, generate_improvement_feedback
    ENHANCED_EVALUATION_AVAILABLE = True
    log_info("Enhanced evaluation module loaded successfully")
except ImportError as e:
    log_error("Enhanced evaluation module not available", e)
    ENHANCED_EVALUATION_AVAILABLE = False

# Check if guideline evaluation module is available
try:
    from guideline_evaluation import evaluate_response_with_guidelines
    GUIDELINE_EVALUATION_AVAILABLE = True
    log_info("Guideline evaluation module loaded successfully")
except ImportError as e:
    log_error("Guideline evaluation module not available", e)
    GUIDELINE_EVALUATION_AVAILABLE = False

def evaluate_user_prompt(prompt, task_id):
    """
    Evaluate a user's prompt.
    
    Args:
        prompt (str): The user's prompt
        task_id (str): The ID of the task
        
    Returns:
        dict: The evaluation results
    """
    # Default evaluation data
    evaluation_data = None
    prompt_dimensions_data = {}  # For UI display of detailed feedback
    grade = "bad"  # Default in case of full failure
    similarity_score = 0  # Default in case of full failure (0-100 scale)
    feedback_details = ["Error: Evaluation could not be performed."]  # Default
    # Default evaluation_results for process_task_completion
    evaluation_results = {"overall_score": 0, "meets_requirements": False}
    
    # Try to use the prompt evaluation module
    if PROMPT_EVALUATION_AVAILABLE:
        try:
            log_info(f"Attempting LLM-based evaluation with prompt_evaluation.py for task {task_id}")
            evaluation_data = evaluate_prompt(prompt, task_id)
            log_info(f"prompt_evaluation.py result: {evaluation_data}")
            
            if evaluation_data and evaluation_data.get("status") == "success":
                grade = evaluation_data.get("grade", "bad")
                similarity_score = evaluation_data.get("overall_score", 0)  # This is 0-100
                feedback_details = evaluation_data.get("feedback_details", ["Evaluation successful but no details provided."])
                prompt_dimensions_data = evaluation_data.get("dimensions", {})
                
                # Keep the original similarity_score (0-100 scale) for display
                # Don't divide by 10 for display purposes
                current_overall_score_for_display = similarity_score
                
                evaluation_results = {
                    "overall_score": similarity_score / 10 if similarity_score > 10 else similarity_score,  # Scaled 0-10 score for internal use
                    "meets_requirements": evaluation_data.get("meets_requirements", False)
                }
                log_info(f"Processed evaluation data: grade={grade}, similarity_score={similarity_score}, meets_requirements={evaluation_results['meets_requirements']}")
            else:
                # Handles case where evaluate_prompt might return a non-success status or unexpected structure
                log_error(f"Prompt evaluation was called but did not return a successful status or valid data. Data: {evaluation_data}")
                # Keep default failure values set above (Option A fallback)
                feedback_details = ["Error: Detailed evaluation is currently unavailable. Please try again later."]
                # Log the error but don't add a system message to the UI
                log_info("Evaluation issue occurred but continuing with basic evaluation")
        except Exception as e:
            log_error(f"LLM-based evaluation (prompt_evaluation.py) failed critically", e)
            # Use Option A fallback strategy - hard fail with basic score
            grade = "bad"
            similarity_score = 0
            feedback_details = ["Error: Detailed evaluation is currently unavailable due to a system issue. Please try again later."]
            prompt_dimensions_data = {}
            evaluation_results = {"overall_score": 0, "meets_requirements": False}
            
            # Log the error but don't add a system message to the UI
            log_info("Evaluation service error occurred but continuing with basic evaluation")
    else:
        # Fallback to basic evaluation if prompt_evaluation.py is not available
        log_info("Prompt evaluation module not available, using basic evaluation")
        
        # Basic evaluation based on prompt length
        prompt_length = len(prompt)
        if prompt_length > 500:
            grade = "good"
            similarity_score = 85
            feedback_details = ["Your prompt is detailed and comprehensive."]
            evaluation_results = {"overall_score": 8.5, "meets_requirements": True}
        elif prompt_length > 200:
            grade = "okay"
            similarity_score = 65
            feedback_details = ["Your prompt is adequate but could be more detailed."]
            evaluation_results = {"overall_score": 6.5, "meets_requirements": True}
        else:
            grade = "bad"
            similarity_score = 35
            feedback_details = ["Your prompt is too short and lacks necessary details."]
            evaluation_results = {"overall_score": 3.5, "meets_requirements": False}
    
    # Return the evaluation results
    return {
        "grade": grade,
        "similarity_score": similarity_score,
        "feedback_details": feedback_details,
        "prompt_dimensions_data": prompt_dimensions_data,
        "evaluation_results": evaluation_results
    }

def determine_meets_requirements(grade, overall_score, manager):
    """
    Determine if the prompt meets the requirements.
    
    Args:
        grade (str): The grade (good, okay, bad)
        overall_score (float): The overall score
        manager (str): The manager ID
        
    Returns:
        bool: Whether the prompt meets the requirements
    """
    # Special handling for certain managers
    if manager == "manager" and manager.get("name") == "Michael Rodriguez":
        # For Michael's tasks, convert the overall score to 0-10 scale if needed
        if overall_score > 10:
            normalized_score = overall_score / 10
        else:
            normalized_score = overall_score
        
        log_info(f"Michael Rodriguez task: Normalized score for meets_requirements check: {normalized_score}")
        
        # Use the normalized score (0-10 scale) to determine if the task meets requirements
        return (
            grade == "good" or
            (grade == "okay" and normalized_score >= 5) or
            normalized_score >= 7 or
            (normalized_score >= 6)  # Pass if score is 6.0 or higher (on 0-10 scale)
        )
    else:
        # For other tasks, use the original logic
        return (
            grade == "good" or
            (grade == "okay" and overall_score >= 50) or
            overall_score >= 70
        )

def calculate_points_earned(grade):
    """
    Calculate points earned based on the grade.
    
    Args:
        grade (str): The grade (good, okay, bad)
        
    Returns:
        int: The points earned
    """
    return 10 if grade == "good" else (5 if grade == "okay" else 0)
