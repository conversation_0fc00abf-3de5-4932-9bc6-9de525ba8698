from django.core.management.base import BaseCommand
from django.db import models
from django.utils import timezone
from datetime import timedelta
from game.models import GameSession

class Command(BaseCommand):
    help = 'Clean up duplicate game sessions and old anonymous sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Delete anonymous sessions older than this many days (default: 7)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days_old = options['days']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Clean up duplicate sessions for authenticated users
        self.stdout.write('Checking for duplicate authenticated user sessions...')
        
        users_with_multiple_sessions = GameSession.objects.filter(
            user__isnull=False
        ).values('user').annotate(
            session_count=models.Count('id')
        ).filter(session_count__gt=1)
        
        total_duplicates_removed = 0
        
        for user_data in users_with_multiple_sessions:
            user_id = user_data['user']
            session_count = user_data['session_count']
            
            sessions = GameSession.objects.filter(
                user_id=user_id
            ).order_by('-updated_at')
            
            # Keep the most recent session, delete others
            sessions_to_delete = sessions[1:]
            
            self.stdout.write(
                f"User {user_id} has {session_count} sessions. "
                f"Keeping most recent, {'would delete' if dry_run else 'deleting'} {len(sessions_to_delete)} duplicates."
            )
            
            for session in sessions_to_delete:
                if not dry_run:
                    session.delete()
                total_duplicates_removed += 1
                self.stdout.write(f"  - {'Would delete' if dry_run else 'Deleted'} session {session.id}")
        
        # Clean up old anonymous sessions
        self.stdout.write(f'\nChecking for anonymous sessions older than {days_old} days...')
        
        cutoff_date = timezone.now() - timedelta(days=days_old)
        old_sessions = GameSession.objects.filter(
            user__isnull=True,
            updated_at__lt=cutoff_date
        )
        
        old_session_count = old_sessions.count()
        
        if old_session_count > 0:
            self.stdout.write(
                f"Found {old_session_count} old anonymous sessions. "
                f"{'Would delete' if dry_run else 'Deleting'} them..."
            )
            
            if not dry_run:
                old_sessions.delete()
        else:
            self.stdout.write("No old anonymous sessions found.")
        
        # Clean up sessions with no session_id and no user (orphaned sessions)
        self.stdout.write('\nChecking for orphaned sessions...')
        
        orphaned_sessions = GameSession.objects.filter(
            user__isnull=True,
            session_id__isnull=True
        )
        
        orphaned_count = orphaned_sessions.count()
        
        if orphaned_count > 0:
            self.stdout.write(
                f"Found {orphaned_count} orphaned sessions. "
                f"{'Would delete' if dry_run else 'Deleting'} them..."
            )
            
            if not dry_run:
                orphaned_sessions.delete()
        else:
            self.stdout.write("No orphaned sessions found.")
        
        # Summary
        self.stdout.write('\n' + '='*50)
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN SUMMARY:'))
            self.stdout.write(f"Would remove {total_duplicates_removed} duplicate authenticated sessions")
            self.stdout.write(f"Would remove {old_session_count} old anonymous sessions")
            self.stdout.write(f"Would remove {orphaned_count} orphaned sessions")
            self.stdout.write(f"Total sessions that would be cleaned: {total_duplicates_removed + old_session_count + orphaned_count}")
        else:
            self.stdout.write(self.style.SUCCESS('CLEANUP COMPLETE:'))
            self.stdout.write(f"Removed {total_duplicates_removed} duplicate authenticated sessions")
            self.stdout.write(f"Removed {old_session_count} old anonymous sessions")
            self.stdout.write(f"Removed {orphaned_count} orphaned sessions")
            self.stdout.write(f"Total sessions cleaned: {total_duplicates_removed + old_session_count + orphaned_count}")
        
        # Show remaining session count
        remaining_sessions = GameSession.objects.count()
        self.stdout.write(f"Remaining sessions in database: {remaining_sessions}")
