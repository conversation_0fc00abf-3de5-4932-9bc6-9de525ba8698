"""
Prompt Evaluation Module for Widget Makers Game

This module provides comprehensive evaluation of user prompts based on six key dimensions:
1. Clarity - How clear and unambiguous the prompt is
2. Context - Whether the prompt provides necessary background information
3. Completeness - Whether the prompt includes all necessary details
4. Task Alignment - How well the prompt aligns with the intended task
5. Output Constraints - Whether the prompt specifies format, tone, style, etc.
6. Model Awareness - Whether the prompt respects the model's capabilities and limitations

Each dimension is scored on a scale of 0-100, and the overall score is a weighted average.
"""

import logging
import re
import json
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import LLM response generator
try:
    from .llm_response_generator_openai_format import generate_response_with_llm
    LLM_RESPONSE_AVAILABLE = True
    logging.info("LLM response generator loaded successfully for prompt evaluation")
except ImportError as e:
    logging.warning(f"LLM response generator not available for prompt evaluation: {str(e)}. Using simplified evaluation.")
    LLM_RESPONSE_AVAILABLE = False

# Constants
DIMENSION_WEIGHTS = {
    "clarity": 1.0,
    "context": 1.0,
    "completeness": 1.0,
    "task_alignment": 1.0,
    "output_constraints": 1.0,
    "model_awareness": 1.0
}

def evaluate_prompt(prompt, task_id):
    """
    Evaluate a user's prompt using the six dimensions of prompt quality.
    This function exclusively uses LLM-based evaluation and does not fall back to simplified_evaluation.

    Args:
        prompt (str): The user's prompt to evaluate
        task_id (str): Identifier for the task

    Returns:
        dict: Evaluation results including scores and feedback
    """
    # Directly use LLM-based evaluation.
    # The try-except block here is to ensure that if llm_based_evaluation
    # itself has an internal issue before making the LLM call or during parsing,
    # it still propagates an error rather than silently failing or returning None.
    # The primary error handling for the LLM call itself is within llm_based_evaluation.
    try:
        # Use LLM for comprehensive evaluation
        result = llm_based_evaluation(prompt, task_id)

        # Check if the result indicates an error (e.g., offline)
        if result.get("status") == "error":
            # Pass the error status back to the caller
            logging.warning(f"LLM-based evaluation returned an error: {result.get('message')}")
            return result

        if result.get("status") == "success": # Assuming llm_based_evaluation or parse_evaluation_response sets a status
            return result
        else:
            # If llm_based_evaluation indicates a failure (e.g. parsing failed, didn't return expected structure)
            # we should raise an exception to be caught by the caller.
            logging.error(f"LLM-based evaluation did not return a success status. Result: {result}")
            raise ValueError("LLM-based prompt evaluation failed to produce a valid success result.")
    except Exception as e:
        logging.error(f"Error directly calling or processing llm_based_evaluation: {str(e)}")
        # Return an error status instead of falling back to simplified evaluation
        return {
            "status": "error",
            "error_type": "offline",
            "message": "Evaluation service unavailable. Please check your internet connection and try again."
        }

def llm_based_evaluation(prompt, task_id):
    """
    Evaluate a prompt using LLM-based assessment of the six dimensions.
    This function does not fall back to simplified_evaluation if the LLM call fails.

    Args:
        prompt (str): The user's prompt to evaluate
        task_id (str): Identifier for the task

    Returns:
        dict: Evaluation results including scores and feedback
    """
    if not LLM_RESPONSE_AVAILABLE:
        logging.warning("LLM response generator not available. Using simplified evaluation.")
        return simplified_evaluation(prompt, is_fallback=True, task_id=task_id)

    # Create the evaluation prompt in parts to avoid f-string issues
    intro = f"You are evaluating a user's prompt for an AI assistant. The prompt is for a task related to: {task_id}.\n\nUser's Prompt:\n{prompt}\n\n"

    instructions = """Evaluate this prompt on the following six aspects that make a good AI prompt. Use simple, non-technical language in your feedback that would be understandable to someone with no AI experience:

1. Clarity: Is the prompt clear about what it's asking for?
2. Context: Does the prompt provide necessary background information?
3. Completeness: Does the prompt include all required details?
4. Task Alignment: Is the prompt aligned with the intended task?
5. Output Constraints: Does the prompt specify any format or style preferences?
6. Model Awareness: Is the prompt reasonable for an AI assistant to handle?

For each dimension, provide:
- A score from 0-100
- Brief feedback explaining the score
- A suggestion for improvement if the score is below 90

Also provide:
- An overall score from 0-100
- A summary of the prompt's strengths and weaknesses
- 2-3 specific suggestions for improving the prompt

Format your response as a JSON object with the following structure:
{
  "dimensions": {
    "clarity": {
      "score": 85,
      "feedback": "The prompt clearly states...",
      "suggestions": ["Consider adding..."]
    },
    // Repeat for all six dimensions
  },
  "overall_score": 82,
  "summary": "This prompt is generally effective but...",
  "improvement_suggestions": [
    "Add more context about...",
    "Specify the desired format..."
  ]
}
"""

    # Combine the parts
    evaluation_prompt = intro + instructions

    try:
        # Call the LLM with the evaluation prompt
        evaluation_response = generate_response_with_llm(evaluation_prompt, "prompt_evaluation")

        # Check if the response indicates an offline or error condition
        if evaluation_response.startswith("I apologize") and ("unable to" in evaluation_response or "technical difficulties" in evaluation_response):
            logging.warning("LLM service appears to be offline or experiencing issues")
            raise ConnectionError("LLM service appears to be offline")

        logging.info(f"Received evaluation response for prompt")

        # Parse the LLM response to extract scores and feedback
        return parse_evaluation_response(evaluation_response, prompt)
    except Exception as e:
        logging.error(f"Error in LLM evaluation: {str(e)}")
        # Return a clear error status that will be caught by the caller
        return {
            "status": "error",
            "error_type": "offline",
            "message": "Evaluation service unavailable. Please check your internet connection and try again."
        }

def parse_evaluation_response(evaluation_text, prompt):
    """
    Parse the evaluation response from the LLM.
    This function does not fall back to simplified_evaluation if parsing fails.

    Args:
        evaluation_text (str): The LLM's evaluation response
        prompt (str): The original prompt (for regex extraction)

    Returns:
        dict: Parsed evaluation results
    """
    try:
        # Try to extract JSON from the response
        json_match = re.search(r'({[\s\S]*})', evaluation_text)
        if json_match:
            json_str = json_match.group(1)
            try:
                evaluation_data = json.loads(json_str)

                # Validate the structure
                if "dimensions" in evaluation_data and "overall_score" in evaluation_data:
                    # Calculate grade based on overall score
                    grade = calculate_grade(evaluation_data["overall_score"])

                    # Determine if the prompt meets requirements
                    # Only "good" grade meets requirements
                    meets_requirements = (grade == "good")

                    # Generate feedback details
                    feedback_details = generate_feedback_details(evaluation_data)

                    # Return the parsed evaluation results
                    return {
                        "status": "success",
                        "grade": grade,
                        "overall_score": evaluation_data["overall_score"],
                        "dimensions": evaluation_data["dimensions"],
                        "summary": evaluation_data.get("summary", ""),
                        "improvement_suggestions": evaluation_data.get("improvement_suggestions", []),
                        "feedback_details": feedback_details,
                        "meets_requirements": meets_requirements
                    }
            except json.JSONDecodeError as e:
                logging.error(f"Error parsing JSON from evaluation response: {str(e)}")
                logging.error(f"JSON string: {json_str}")
                raise
    except Exception as e:
        logging.error(f"Error parsing evaluation response: {str(e)}")
        logging.error(f"Evaluation text: {evaluation_text}")
        raise

    # If we get here, we couldn't parse the response
    logging.error(f"Could not parse evaluation response: {evaluation_text}")
    raise ValueError("Could not parse evaluation response")

def generate_feedback_details(evaluation_data):
    """
    Generate feedback details from the evaluation data.

    Args:
        evaluation_data (dict): The parsed evaluation data

    Returns:
        list: Feedback details
    """
    feedback_details = []

    # Add overall score first, formatted as per the screenshot
    if "overall_score" in evaluation_data:
        feedback_details.append(f"Overall score: {evaluation_data['overall_score']}/100")

    # Add dimension-specific feedback, formatted as per the screenshot
    if "dimensions" in evaluation_data:
        for dimension, data in evaluation_data["dimensions"].items():
            score = data.get("score", "N/A")
            feedback_text = data.get("feedback", "No feedback provided.")
            feedback_details.append(f"{dimension.capitalize()}: {score}/100 - {feedback_text}")

    # The "Suggestions for improvement" are handled separately by the frontend,
    # using the "improvement_suggestions" field from the evaluation_data.
    # So, we don't add them to this specific feedback_details list.

    return feedback_details

def simplified_evaluation(prompt, is_fallback=False, task_id="cover_letter"):
    """
    Provide a simplified evaluation based on prompt length and basic heuristics.

    Args:
        prompt (str): The user's prompt
        is_fallback (bool): Whether this is being used as a fallback due to connection issues
        task_id (str): Identifier for the task

    Returns:
        dict: Simplified evaluation results
    """
    # Basic scoring based on prompt length and complexity
    prompt_length = len(prompt)

    # Score based on length (0-100) - more lenient with short prompts
    length_score = min(100, max(30, prompt_length // 2))

    # Check for question marks (indicates clarity)
    question_score = 60 if "?" in prompt else 40

    # Check for specific instructions (indicates completeness)
    specific_words = ["list", "explain", "describe", "analyze", "compare", "summarize", "create", "write", "draft", "cover letter", "application", "job", "position", "skills", "qualifications", "experience"]

    # For cover letter task, add more specific keywords
    if task_id == "cover_letter":
        specific_words.extend(["junior assistant", "administrative", "office", "support", "calendar", "meetings", "filing", "communication", "organization", "detail", "microsoft office", "problem-solving", "multitask"])

    specificity_score = 0
    for word in specific_words:
        if word in prompt.lower():
            specificity_score += 15
    specificity_score = min(100, specificity_score)

    # Check for context indicators
    context_words = ["because", "since", "as", "given", "considering", "background", "context", "situation", "scenario", "company", "organization", "role", "position", "job"]
    context_indicator = 0
    for word in context_words:
        if word in prompt.lower():
            context_indicator += 20
    context_indicator = min(100, context_indicator)

    # Check for output constraints
    constraint_words = ["format", "tone", "style", "length", "word count", "paragraph", "bullet", "point", "list", "formal", "informal", "professional", "friendly", "enthusiastic", "concise", "detailed"]
    constraint_score = 0
    for word in constraint_words:
        if word in prompt.lower():
            constraint_score += 20
    constraint_score = min(100, constraint_score)

    # Calculate dimension scores with more sophisticated heuristics - more lenient with short prompts
    clarity_score = min(90, (length_score + question_score) // 2)
    if prompt_length < 15:
        clarity_score = min(clarity_score, 60)  # More lenient with very short prompts

    # Context score based on length and context indicators
    context_base = max(20, min(70, prompt_length // 6))
    context_score = min(85, context_base + context_indicator)

    # Completeness based on length and specificity - more lenient with short prompts
    completeness_score = min(85, (length_score + specificity_score) // 2)
    if prompt_length < 30:
        completeness_score = min(completeness_score, 65)  # More lenient with short prompts

    # Task alignment - check for task-specific keywords
    task_alignment_score = 70  # Base score

    # For cover letter task, check for specific keywords
    if task_id == "cover_letter":
        cover_letter_keywords = ["cover letter", "application", "job", "position", "junior assistant", "rwenzori", "solar", "administrative", "support"]
        for word in cover_letter_keywords:
            if word in prompt.lower():
                task_alignment_score += 5

        # Cap at 95
        task_alignment_score = min(95, task_alignment_score)
    else:
        # For other tasks, use a reasonable default
        task_alignment_score = random.randint(85, 95)

    # Output constraints
    output_constraints_score = constraint_score

    # Model awareness - be more lenient
    model_awareness_score = random.randint(70, 90)  # Randomize for demo purposes

    # Calculate overall score with weighted dimensions
    # Give more weight to clarity and completeness
    overall_score = (clarity_score * 1.2 + context_score + completeness_score * 1.2 +
                    task_alignment_score + output_constraints_score + model_awareness_score) // 6.4

    # If this is a fallback evaluation, cap the score to avoid misleading confidence
    if is_fallback:
        # Cap the overall score to be more conservative when offline
        overall_score = min(overall_score, 75)

    # Calculate grade based on overall score
    grade = calculate_grade(overall_score)

    # Determine if the prompt meets requirements
    # Only "good" grade meets requirements
    meets_requirements = (grade == "good")

    # Create dimensions dictionary with more detailed feedback for cover letter task
    dimensions = {
        "clarity": {
            "score": clarity_score,
            "feedback": "The prompt is very clear about what it's asking for. A cover letter for the position. The provided job advertisement gives a clear understanding of the role and company.",
            "suggestions": ["While already very clear, consider explicitly stating the desired tone of the cover letter (e.g., professional, enthusiastic)"]
        },
        "context": {
            "score": context_score,
            "feedback": "The prompt provides excellent context by including the full job advertisement. This gives the AI a solid understanding of the company, role, and requirements.",
            "suggestions": ["You could add a brief sentence about your background or experience to give the AI even more context, although the AI should be able to make assumptions based on the job description"]
        },
        "completeness": {
            "score": completeness_score,
            "feedback": "The prompt is mostly complete, providing the necessary information to draft a cover letter. It includes the job description, requirements, and company details.",
            "suggestions": ["Consider specifying any particular skills or experiences you want to highlight in the cover letter. This would help the AI tailor the letter even more effectively"]
        },
        "task_alignment": {
            "score": task_alignment_score,
            "feedback": "The prompt is perfectly aligned with the task. It explicitly requests a 'cover letter,' leaving no room for ambiguity.",
            "suggestions": ["N/A - The task alignment is already excellent"]
        },
        "output_constraints": {
            "score": output_constraints_score,
            "feedback": "The prompt lacks specific instructions on the desired length, tone, or style of the cover letter. While it's implied to be professional, more explicit constraints would be helpful.",
            "suggestions": ["Specify the desired length of the cover letter (e.g., one page, under 500 words)", "Indicate the desired tone or style (e.g., formal, enthusiastic, confident)"]
        },
        "model_awareness": {
            "score": model_awareness_score,
            "feedback": "The prompt is perfectly suited for an AI, as generating a cover letter based on provided information is a common and well-supported task.",
            "suggestions": ["N/A - The task is very reasonable for an AI"]
        }
    }

    # Generate summary based on overall score
    if overall_score >= 90:
        summary = "This is a very strong prompt overall. It provides clear instructions, sufficient context, and is well-aligned with the capabilities of an AI. The main area for improvement is adding more specific output constraints regarding the desired length, tone, and style of the cover letter."
    elif overall_score >= 75:
        summary = "This is a good prompt that clearly communicates what you want. Adding more specific output constraints and personal background information would make it even better."
    elif overall_score >= 60:
        summary = "This prompt is okay but could be improved with more clarity about the specific requirements for the cover letter and more context about your background."
    else:
        summary = "This prompt needs improvement. Try to be more specific about what you want in a cover letter and provide more context about the position and your qualifications."

    # Set specific improvement suggestions for cover letters
    improvement_suggestions = [
        "Specify the desired length of the cover letter (e.g., one page, under 500 words).",
        "Indicate the desired tone or style (e.g., formal, enthusiastic, confident).",
        "Briefly mention any specific skills or experiences you want to emphasize in the cover letter."
    ]

    # Generate feedback details for simplified_evaluation (keeping its original format for now,
    # as the main focus is on the LLM-based evaluation's feedback_details)
    simplified_feedback_details = []
    if "overall_score" in locals() and overall_score is not None: # Check if overall_score was calculated
        simplified_feedback_details.append(f"Overall score: {int(overall_score)}/100") # Ensure overall_score is int for display
    for dim_name, dim_data in dimensions.items():
        simplified_feedback_details.append(f"{dim_name.capitalize()}: {dim_data['score']}/100 - {dim_data['feedback']}")


    return {
        "status": "success",
        "grade": grade,
        "overall_score": overall_score,
        "dimensions": dimensions,
        "summary": summary,
        "improvement_suggestions": improvement_suggestions,
        "feedback_details": simplified_feedback_details, # Use the locally generated list
        "meets_requirements": meets_requirements,
        "prompt_dimensions": dimensions # Retained for potential detailed view, though screenshot implies summary/suggestions are primary
        # Removing redundant keys:
        # "prompt_evaluation_summary": summary, (use "summary")
        # "prompt_improvement_suggestions": improvement_suggestions (use "improvement_suggestions")
    }

def calculate_grade(score):
    """
    Calculate a grade based on the overall score.

    Args:
        score (int): The overall score (0-100)

    Returns:
        str: The grade ('good', 'okay', or 'bad')
    """
    if score >= 80:
        return "good"
    elif score >= 60:
        return "okay"
    else:
        return "bad"
