"""
Role progression structure for the Context-Aware Game.
This module contains the role progression structure and related functions.
"""

# Role progression structure - Aligned with GAME_ARCHITECTURE_GUIDE.md
# This follows the simplified 10-role progression path as specified in the architecture guide
ROLE_PROGRESSION = {
    # Entry Level - Only 1 task required (Entry Level Exception)
    "applicant": {
        "next_role": "junior_assistant",
        "challenges_required": 1,  # Entry Level Exception: Applicant only needs 1 task (cover letter)
        "promotion_message": "Congratulations! You've been hired as a Junior Assistant at Rwenzori Innovations Ltd. You're now officially part of the team!"
    },

    # All subsequent roles require exactly 3 tasks (Standard Progression)
    "junior_assistant": {
        "next_role": "sales_associate",
        "challenges_required": 3,
        "promotion_message": "Great work! You've been promoted to Sales Associate. Your communication skills will be valuable in this role!"
    },
    "sales_associate": {
        "next_role": "marketing_associate",
        "challenges_required": 3,
        "promotion_message": "Your sales skills have impressed us! You're now being promoted to Marketing Associate where you'll help develop our marketing strategies."
    },
    "marketing_associate": {
        "next_role": "senior_marketing_specialist",
        "challenges_required": 3,
        "promotion_message": "Your marketing expertise has been noticed! You've been promoted to Senior Marketing Specialist."
    },
    "senior_marketing_specialist": {
        "next_role": "marketing_manager",
        "challenges_required": 3,
        "promotion_message": "Excellent work! You've been promoted to Marketing Manager. You'll now lead marketing initiatives."
    },
    "marketing_manager": {
        "next_role": "senior_marketing_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership has been outstanding! You've been promoted to Senior Marketing Manager."
    },
    "senior_marketing_manager": {
        "next_role": "marketing_director",
        "challenges_required": 3,
        "promotion_message": "Your strategic vision has earned you a promotion to Marketing Director!"
    },
    "marketing_director": {
        "next_role": "vp_marketing",
        "challenges_required": 3,
        "promotion_message": "Your exceptional leadership has earned you a promotion to Vice President of Marketing!"
    },
    "vp_marketing": {
        "next_role": "cmo",
        "challenges_required": 3,
        "promotion_message": "Your outstanding performance as VP of Marketing has impressed the board! You've been promoted to Chief Marketing Officer (CMO)."
    },
    "cmo": {
        "next_role": None,  # Final role - game completion
        "challenges_required": 3,
        "promotion_message": "Congratulations! You've reached the pinnacle of the marketing organization as Chief Marketing Officer. You've completed the Corporate Prompt Master game!"
    }
}

def get_next_role(current_role):
    """
    Get the next role in the progression.

    Args:
        current_role (str): The current role

    Returns:
        str or None: The next role or None if there is no next role
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    return role_info.get("next_role")

def get_challenges_required(current_role):
    """
    Get the number of challenges required to advance from the current role.

    Args:
        current_role (str): The current role

    Returns:
        int: The number of challenges required
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    return role_info.get("challenges_required", 3)

def get_promotion_message(current_role):
    """
    Get the promotion message for advancing from the current role.

    Args:
        current_role (str): The current role

    Returns:
        str: The promotion message with enhanced formatting
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    base_message = role_info.get("promotion_message", f"Congratulations! You've been promoted to {get_next_role(current_role)}!")

    # Add markdown formatting to make the message more prominent
    # This matches the Flask app's style
    enhanced_message = f"""## 🎉 Congratulations! 🎉

{base_message}

### How to Improve Your Request:

#### Try these tips:
- Be specific about what you want in your next task
- Provide context about your situation
- Specify the format you prefer for the response
"""

    return enhanced_message

def get_role_info(role):
    """
    Get information about a role.

    Args:
        role (str): The role to get information about

    Returns:
        dict: The role information or an empty dict if not found
    """
    return ROLE_PROGRESSION.get(role, {})
