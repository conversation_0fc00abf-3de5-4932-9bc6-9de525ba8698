// Chart Double-Click Zoom Functionality

// Initialize the double-click zoom functionality
function initC<PERSON><PERSON><PERSON>Zoom() {
    console.log('Initializing chart double-click zoom functionality');

    // Setup org chart double-click zoom
    setupOrgChartZoom();

    // Setup role progression double-click zoom
    setupRoleProgressionZoom();
}

// Setup organization chart double-click zoom
function setupOrgChartZoom() {
    console.log('Setting up org chart zoom');

    // Find the org chart container
    const orgChartContainer = document.getElementById('org-chart-container');
    if (!orgChartContainer) {
        console.error('Org chart container not found');
        return;
    }

    console.log('Found org chart container:', orgChartContainer);

    // Add zoom class
    orgChartContainer.classList.add('double-click-zoom-container');

    // Remove any existing indicators
    const existingIndicator = orgChartContainer.querySelector('.zoom-indicator');
    if (existingIndicator) {
        orgChartContainer.removeChild(existingIndicator);
    }

    // Add zoom indicator with enhanced icon
    const indicator = document.createElement('div');
    indicator.className = 'zoom-indicator';
    indicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>';
    indicator.title = 'Double-click to zoom';
    orgChartContainer.appendChild(indicator);

    // Remove any existing overlays
    const existingOverlay = document.getElementById('org-chart-zoom-overlay');
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }

    // Create zoom overlay
    const overlay = document.createElement('div');
    overlay.className = 'zoom-overlay org-chart-zoom';
    overlay.id = 'org-chart-zoom-overlay';

    // Create zoom content
    const content = document.createElement('div');
    content.className = 'zoom-content';

    // Create close button with enhanced styling
    const closeBtn = document.createElement('button');
    closeBtn.className = 'zoom-close';
    closeBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
    closeBtn.title = 'Close';
    closeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        overlay.classList.remove('visible');
    });

    // Create scroll indicator
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14M5 12h14"></path></svg>';
    scrollIndicator.title = 'Scroll to navigate';

    // Add content to overlay
    content.appendChild(closeBtn);
    content.appendChild(scrollIndicator);
    overlay.appendChild(content);

    // Add overlay to document body
    document.body.appendChild(overlay);

    console.log('Created org chart zoom overlay:', overlay);

    // Add double-click event listener
    orgChartContainer.addEventListener('dblclick', function(e) {
        // Don't trigger if double-clicking on a link or button inside the chart
        if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
            return;
        }

        console.log('Double-clicked on org chart');
        updateOrgChartZoomContent();
        overlay.classList.add('visible');
    });

    // Add click event for the indicator
    indicator.addEventListener('click', function(e) {
        e.stopPropagation();
        console.log('Clicked on org chart indicator');
        updateOrgChartZoomContent();
        overlay.classList.add('visible');
    });

    // Add escape key handler to close overlay
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && overlay.classList.contains('visible')) {
            overlay.classList.remove('visible');
        }
    });

    console.log('Org chart zoom setup complete');
}

// Update org chart zoom content
function updateOrgChartZoomContent() {
    console.log('Updating org chart zoom content');

    const overlay = document.getElementById('org-chart-zoom-overlay');
    if (!overlay) {
        console.error('Org chart zoom overlay not found');
        return;
    }

    const content = overlay.querySelector('.zoom-content');
    if (!content) {
        console.error('Zoom content container not found');
        return;
    }

    // Get the current org chart content
    const orgChart = document.getElementById('org-chart');
    if (!orgChart) {
        console.error('Org chart element not found');
        return;
    }

    console.log('Org chart found:', orgChart);

    // Add a title to the zoom modal
    const title = document.createElement('h2');
    title.textContent = 'Company Hierarchy';
    title.className = 'zoom-modal-title';

    // Clear previous content (except close button)
    const closeBtn = content.querySelector('.zoom-close');
    if (closeBtn) {
        content.innerHTML = '';
        content.appendChild(closeBtn);
    } else {
        console.warn('Close button not found, creating new content');
        content.innerHTML = '';

        // Create new close button with enhanced styling
        const newCloseBtn = document.createElement('button');
        newCloseBtn.className = 'zoom-close';
        newCloseBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
        newCloseBtn.title = 'Close';
        newCloseBtn.addEventListener('click', function() {
            overlay.classList.remove('visible');
        });
        content.appendChild(newCloseBtn);
    }

    // Add the title
    content.appendChild(title);

    // Create inner content container
    const contentInner = document.createElement('div');
    contentInner.className = 'zoom-content-inner';

    // Create a container for the org chart
    const chartContainer = document.createElement('div');
    chartContainer.className = 'zoomed-org-chart-container';

    // Clone the org chart content
    const clonedContent = document.createElement('div');
    clonedContent.className = 'org-chart-content';

    // Get the HTML content - try to get the actual content, not just the innerHTML
    let chartHtml = '';

    // First try to get the org-chart-content div if it exists
    const orgChartContent = orgChart.querySelector('.org-chart-content');
    if (orgChartContent) {
        console.log('Found org-chart-content element, using its HTML');
        chartHtml = orgChartContent.outerHTML;
    } else {
        // Otherwise use the innerHTML of the org chart
        console.log('Using org chart innerHTML');
        chartHtml = orgChart.innerHTML;
    }

    console.log('Org chart HTML length:', chartHtml.length);
    console.log('Org chart HTML preview:', chartHtml.substring(0, 100) + '...');

    // Set the content - make sure to handle any undefined or empty content
    if (chartHtml && chartHtml.trim()) {
        // If we're using the org-chart-content element's outerHTML, we don't need to wrap it
        if (orgChartContent) {
            chartContainer.innerHTML = chartHtml;
        } else {
            // Otherwise wrap it in a div with the org-chart-content class
            chartContainer.innerHTML = `<div class="org-chart-content">${chartHtml}</div>`;
        }
    } else {
        // Try to fetch the org chart HTML from the server as a fallback
        console.log('Chart HTML is empty, trying to fetch from server');

        // Show a loading placeholder
        chartContainer.innerHTML = '<div class="chart-placeholder">Loading organization chart...</div>';

        // Try to fetch the org chart from the server
        fetch('/get_game_state')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.org_chart_html) {
                    console.log('Received org chart HTML from server');
                    chartContainer.innerHTML = data.org_chart_html;

                    // Update the scale after content is loaded
                    setScale(chartContainer.querySelector('.org-chart-content') || chartContainer, 1);
                } else {
                    console.error('Failed to get org chart HTML from server');
                    chartContainer.innerHTML = '<div class="chart-placeholder">Failed to load organization chart</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching org chart HTML:', error);
                chartContainer.innerHTML = '<div class="chart-placeholder">Error loading organization chart</div>';
            });
    }

    // Add chart container to inner container
    contentInner.appendChild(chartContainer);

    // Add inner container to content
    content.appendChild(contentInner);

    // Add zoom controls
    addZoomControls(content, chartContainer.querySelector('.org-chart-content') || chartContainer);

    // Set initial scale
    setScale(chartContainer.querySelector('.org-chart-content') || chartContainer, 1);

    console.log('Org chart zoom content updated');
}

// Setup role progression double-click zoom
function setupRoleProgressionZoom() {
    console.log('Setting up role progression zoom');

    // Find the role progression container
    const roleProgressionContainer = document.getElementById('role-progression-container');
    if (!roleProgressionContainer) {
        console.error('Role progression container not found');
        return;
    }

    console.log('Found role progression container:', roleProgressionContainer);

    // Add zoom class
    roleProgressionContainer.classList.add('double-click-zoom-container');

    // Remove any existing indicators
    const existingIndicator = roleProgressionContainer.querySelector('.zoom-indicator');
    if (existingIndicator) {
        roleProgressionContainer.removeChild(existingIndicator);
    }

    // Add zoom indicator with enhanced icon
    const indicator = document.createElement('div');
    indicator.className = 'zoom-indicator';
    indicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>';
    indicator.title = 'Double-click to zoom';
    roleProgressionContainer.appendChild(indicator);

    // Remove any existing overlays
    const existingOverlay = document.getElementById('role-progression-zoom-overlay');
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }

    // Create zoom overlay
    const overlay = document.createElement('div');
    overlay.className = 'zoom-overlay role-progression-zoom';
    overlay.id = 'role-progression-zoom-overlay';

    // Create zoom content
    const content = document.createElement('div');
    content.className = 'zoom-content';

    // Create close button with enhanced styling
    const closeBtn = document.createElement('button');
    closeBtn.className = 'zoom-close';
    closeBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
    closeBtn.title = 'Close';
    closeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        overlay.classList.remove('visible');
    });

    // Create scroll indicator
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14M5 12h14"></path></svg>';
    scrollIndicator.title = 'Scroll to navigate';

    // Add content to overlay
    content.appendChild(closeBtn);
    content.appendChild(scrollIndicator);
    overlay.appendChild(content);

    // Add overlay to document body
    document.body.appendChild(overlay);

    console.log('Created role progression zoom overlay:', overlay);

    // Add double-click event listener
    roleProgressionContainer.addEventListener('dblclick', function(e) {
        // Don't trigger if double-clicking on a link or button inside the chart
        if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
            return;
        }

        console.log('Double-clicked on role progression');
        updateRoleProgressionZoomContent();
        overlay.classList.add('visible');
    });

    // Add click event for the indicator
    indicator.addEventListener('click', function(e) {
        e.stopPropagation();
        console.log('Clicked on role progression indicator');
        updateRoleProgressionZoomContent();
        overlay.classList.add('visible');
    });

    // Add escape key handler to close overlay
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && overlay.classList.contains('visible')) {
            overlay.classList.remove('visible');
        }
    });

    console.log('Role progression zoom setup complete');
}

// Update role progression zoom content
function updateRoleProgressionZoomContent() {
    console.log('Updating role progression zoom content');

    const overlay = document.getElementById('role-progression-zoom-overlay');
    if (!overlay) {
        console.error('Role progression zoom overlay not found');
        return;
    }

    const content = overlay.querySelector('.zoom-content');
    if (!content) {
        console.error('Zoom content container not found');
        return;
    }

    // Get the current role progression content
    const roleProgression = document.getElementById('role-progression-content');
    if (!roleProgression) {
        console.error('Role progression element not found');
        return;
    }

    console.log('Role progression found:', roleProgression);

    // Add a title to the zoom modal
    const title = document.createElement('h2');
    title.textContent = 'Career Path';
    title.className = 'zoom-modal-title';

    // Clear previous content (except close button)
    const closeBtn = content.querySelector('.zoom-close');
    if (closeBtn) {
        content.innerHTML = '';
        content.appendChild(closeBtn);
    } else {
        console.warn('Close button not found, creating new content');
        content.innerHTML = '';

        // Create new close button with enhanced styling
        const newCloseBtn = document.createElement('button');
        newCloseBtn.className = 'zoom-close';
        newCloseBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
        newCloseBtn.title = 'Close';
        newCloseBtn.addEventListener('click', function() {
            overlay.classList.remove('visible');
        });
        content.appendChild(newCloseBtn);
    }

    // Add the title
    content.appendChild(title);

    // Create inner content container
    const contentInner = document.createElement('div');
    contentInner.className = 'zoom-content-inner';

    // Create a container for the role progression
    const progressionContainer = document.createElement('div');
    progressionContainer.className = 'zoomed-role-progression-container';

    // Get the HTML content - try to get the actual content, not just the innerHTML
    let progressionHtml = '';

    // First try to get the role-progression div if it exists
    const roleProgressionDiv = roleProgression.querySelector('.role-progression');
    if (roleProgressionDiv) {
        console.log('Found role-progression element, using its HTML');
        progressionHtml = roleProgressionDiv.outerHTML;
    } else {
        // Otherwise use the innerHTML of the role progression content
        console.log('Using role progression innerHTML');
        progressionHtml = roleProgression.innerHTML;
    }

    console.log('Role progression HTML length:', progressionHtml.length);
    console.log('Role progression HTML preview:', progressionHtml.substring(0, 100) + '...');

    // Set the content - make sure to handle any undefined or empty content
    if (progressionHtml && progressionHtml.trim()) {
        // If we're using the role-progression element's outerHTML, we don't need to wrap it
        if (roleProgressionDiv) {
            progressionContainer.innerHTML = progressionHtml;
        } else {
            // Otherwise wrap it in a div with the role-progression class
            progressionContainer.innerHTML = `<div class="role-progression">${progressionHtml}</div>`;
        }
    } else {
        // Try to fetch the role progression HTML from the server as a fallback
        console.log('Progression HTML is empty, trying to fetch from server');

        // Show a loading placeholder
        progressionContainer.innerHTML = '<div class="chart-placeholder">Loading career progression chart...</div>';

        // Try to fetch the role progression from the server
        fetch('/get_game_state')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.role_progression_html) {
                    console.log('Received role progression HTML from server');
                    progressionContainer.innerHTML = data.role_progression_html;

                    // Update the scale after content is loaded
                    setScale(progressionContainer.querySelector('.role-progression') || progressionContainer, 1);
                } else {
                    console.error('Failed to get role progression HTML from server');
                    progressionContainer.innerHTML = '<div class="chart-placeholder">Failed to load career progression</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching role progression HTML:', error);
                progressionContainer.innerHTML = '<div class="chart-placeholder">Error loading career progression</div>';
            });
    }

    // Add progression container to inner container
    contentInner.appendChild(progressionContainer);

    // Add inner container to content
    content.appendChild(contentInner);

    // Add zoom controls
    addZoomControls(content, progressionContainer.querySelector('.role-progression') || progressionContainer);

    // Set initial scale
    setScale(progressionContainer.querySelector('.role-progression') || progressionContainer, 1);

    console.log('Role progression zoom content updated');
}

// Add zoom controls to the content
function addZoomControls(container, contentElement) {
    // Create zoom controls container
    const controls = document.createElement('div');
    controls.className = 'zoom-controls';

    // Create zoom in button
    const zoomInBtn = document.createElement('button');
    zoomInBtn.className = 'zoom-control-btn zoom-in';
    zoomInBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>';
    zoomInBtn.title = 'Zoom In';
    zoomInBtn.addEventListener('click', function() {
        const currentScale = getScale(contentElement);
        setScale(contentElement, currentScale + 0.2);
    });

    // Create zoom out button
    const zoomOutBtn = document.createElement('button');
    zoomOutBtn.className = 'zoom-control-btn zoom-out';
    zoomOutBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>';
    zoomOutBtn.title = 'Zoom Out';
    zoomOutBtn.addEventListener('click', function() {
        const currentScale = getScale(contentElement);
        setScale(contentElement, Math.max(0.5, currentScale - 0.2));
    });

    // Create reset button
    const resetBtn = document.createElement('button');
    resetBtn.className = 'zoom-control-btn zoom-reset';
    resetBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path></svg>';
    resetBtn.title = 'Reset Zoom';
    resetBtn.addEventListener('click', function() {
        setScale(contentElement, 1);
    });

    // Add buttons to controls
    controls.appendChild(zoomOutBtn);
    controls.appendChild(resetBtn);
    controls.appendChild(zoomInBtn);

    // Add controls to container
    container.appendChild(controls);

    console.log('Zoom controls added with enhanced styling');

    // Add mouse wheel scroll support instead of zoom
    const contentInner = container.querySelector('.zoom-content-inner');
    if (contentInner) {
        contentInner.addEventListener('wheel', function(e) {
            // Don't prevent default to allow natural scrolling
            // This allows the mouse wheel to scroll the chart up and down

            // If user is holding Ctrl key, then zoom instead of scroll
            if (e.ctrlKey) {
                e.preventDefault();
                const currentScale = getScale(contentElement);
                const delta = e.deltaY > 0 ? -0.1 : 0.1;
                setScale(contentElement, Math.max(0.5, currentScale + delta));
            }
            // Otherwise, natural scrolling will occur
        });
    }
}

// Helper function to get current scale
function getScale(element) {
    const transform = element.style.transform;
    if (!transform || !transform.includes('scale')) {
        return 1; // Default scale
    }
    const match = transform.match(/scale\(([^)]+)\)/);
    return match ? parseFloat(match[1]) : 1;
}

// Helper function to set scale with improved centering
function setScale(element, scale) {
    // Limit scale to reasonable bounds
    scale = Math.max(0.5, Math.min(3, scale));

    // Apply the scale with smooth transition
    element.style.transition = 'transform 0.3s ease, margin 0.3s ease';
    element.style.transform = `scale(${scale})`;

    // Adjust margins to center the content better when scaled
    if (scale > 1) {
        // Calculate margins based on scale factor for better centering
        const marginTop = (scale - 1) * 80;
        const marginBottom = (scale - 1) * 100;

        element.style.marginTop = `${marginTop}px`;
        element.style.marginBottom = `${marginBottom}px`;

        // Add some horizontal margin for better appearance
        const marginHorizontal = (scale - 1) * 30;
        element.style.marginLeft = `${marginHorizontal}px`;
        element.style.marginRight = `${marginHorizontal}px`;
    } else {
        // Reset margins when at normal scale or below
        element.style.marginTop = '0';
        element.style.marginBottom = '0';
        element.style.marginLeft = '0';
        element.style.marginRight = '0';
    }

    // Log the current scale for debugging
    console.log(`Chart scaled to: ${scale.toFixed(1)}x`);
}

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure the charts are loaded
    setTimeout(initChartHoverZoom, 1000);

    // Set up additional initialization attempts to ensure charts are properly loaded
    setTimeout(initChartHoverZoom, 2000);
    setTimeout(initChartHoverZoom, 5000);
});

// Re-initialize when window is resized
window.addEventListener('resize', function() {
    // Debounce the resize event
    if (window.resizeTimer) {
        clearTimeout(window.resizeTimer);
    }
    window.resizeTimer = setTimeout(function() {
        initChartHoverZoom();
    }, 250);
});

// Re-initialize when game state changes (e.g., after a promotion)
function reinitializeChartZoom() {
    console.log('Reinitializing chart zoom after game state change');
    setTimeout(initChartHoverZoom, 500);
}

// Expose the reinitialization function to the global scope
window.reinitializeChartZoom = reinitializeChartZoom;

// Function to manually test the org chart zoom
function testOrgChartZoom() {
    console.log('Testing org chart zoom');
    setupOrgChartZoom();

    setTimeout(() => {
        const overlay = document.getElementById('org-chart-zoom-overlay');
        if (overlay) {
            updateOrgChartZoomContent();
            overlay.classList.add('visible');
            console.log('Org chart zoom overlay opened');
        } else {
            console.error('Org chart zoom overlay not found');
        }
    }, 500);
}

// Function to manually test the role progression zoom
function testRoleProgressionZoom() {
    console.log('Testing role progression zoom');
    setupRoleProgressionZoom();

    setTimeout(() => {
        const overlay = document.getElementById('role-progression-zoom-overlay');
        if (overlay) {
            updateRoleProgressionZoomContent();
            overlay.classList.add('visible');
            console.log('Role progression zoom overlay opened');
        } else {
            console.error('Role progression zoom overlay not found');
        }
    }, 500);
}

// Expose functions to global scope
window.initChartHoverZoom = initChartHoverZoom;
window.updateOrgChartZoomContent = updateOrgChartZoomContent;
window.updateRoleProgressionZoomContent = updateRoleProgressionZoomContent;
window.testOrgChartZoom = testOrgChartZoom;
window.testRoleProgressionZoom = testRoleProgressionZoom;
