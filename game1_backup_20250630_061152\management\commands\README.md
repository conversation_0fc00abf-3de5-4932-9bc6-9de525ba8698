# Game Management Commands

This directory contains custom Django management commands for the Prompt Game project.

## Available Commands

### `cleanup_anonymous_players`
Clean up anonymous player data after a configurable time period.

```bash
python manage.py cleanup_anonymous_players [options]
```

Options:
- `--force`: Force cleanup regardless of the time since last cleanup
- `--dry-run`: Show what would be deleted without actually deleting
- `--hours`: Override the number of hours from settings

Example:
```bash
# Run cleanup with default settings
$ python manage.py cleanup_anonymous_players

# Run a dry run to see what would be deleted
$ python manage.py cleanup_anonymous_players --dry-run

# Force cleanup and override hours
$ python manage.py cleanup_anonymous_players --force --hours=12
```

## Scheduling the Cleanup Command

To run the cleanup command automatically, you can use various scheduling methods:

### Windows Task Scheduler

1. Open Task Scheduler
2. Create a new task
3. Set the trigger (e.g., daily at midnight)
4. Set the action to run a program:
   - Program/script: `python`
   - Arguments: `manage.py cleanup_anonymous_players`
   - Start in: `C:\path\to\your\project`

### Cron Job (Linux/Mac)

Add a line to your crontab:

```
0 0 * * * cd /path/to/your/project && python manage.py cleanup_anonymous_players
```

This will run the command daily at midnight.

### Django Management

You can also run the command from the Django admin interface by configuring the settings in:
Admin > Game > Anonymous Player Settings

The cleanup will run automatically based on the configured settings when users access the site.
