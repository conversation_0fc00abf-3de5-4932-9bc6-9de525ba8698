/* Right Sidebar Styles */

/* Enhanced Modern Right Sidebar - Matching Left Sidebar */
.right-sidebar {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-left: 1px solid var(--border-primary);
    padding: var(--space-md);
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* allow scrolling of right sidebar content */
    position: relative;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.right-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

/* Right sidebar toggle button - ONLY APPLY TO NON-MOBILE TOGGLE */
.right-sidebar-toggle:not(.mobile-sidebar-toggle) {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 24px;
    height: 18px;
    cursor: pointer;
    margin-left: 15px;
    z-index: 10;
    position: relative;
    transform: rotate(180deg); /* Rotate to differentiate from left sidebar toggle */
}

.right-sidebar-toggle:not(.mobile-sidebar-toggle) span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: #4a86e8; /* Different color to differentiate */
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hover effect - ONLY FOR NON-MOBILE TOGGLE */
.right-sidebar-toggle:not(.mobile-sidebar-toggle):hover span {
    background-color: #2a66c8;
}

/* Active state when right sidebar is hidden - ONLY FOR NON-MOBILE TOGGLE */
.right-sidebar-hidden .right-sidebar-toggle:not(.mobile-sidebar-toggle) span {
    background-color: #999;
}

/* Enhanced Right Sidebar Header - Matching Left Sidebar */
.right-sidebar-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
    position: relative;
}

.right-sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

.right-sidebar-collapse-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: var(--space-md);
    margin-top: var(--space-xs);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    border-radius: 50%;
    background: var(--bg-elevated);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
}

.right-sidebar-collapse-indicator:hover {
    background: var(--primary-gradient);
    color: var(--text-primary);
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.right-sidebar-title {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.025em;
}

/* Hide zoom overlays that unintentionally cover sidebar */
.role-progression-zoom-overlay,
.org-chart-zoom-overlay,
#role-progression-zoom-overlay,
#org-chart-zoom-overlay {
    display: none !important;
    pointer-events: none !important;
    background: transparent !important;
    opacity: 0 !important;
}

/* Force text visibility in right sidebar */
.right-sidebar, .right-sidebar * {
    color: #ffffff !important;
}

/* Ensure role boxes are readable on dark sidebar */
.right-sidebar .role-box {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    padding: 8px 10px !important;
    margin-bottom: 8px !important;
}
.right-sidebar .role-name {
    font-weight: 600 !important;
    color: #ffffff !important;
}
.right-sidebar .role-status {
    font-size: 0.8rem !important;
    color: #cccccc !important;
}

/* Enhanced Company Hierarchy Section - Matching Left Sidebar Cards */
.right-sidebar .org-chart-container {
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.right-sidebar .org-chart-container h3 {
    font-size: 0.95rem;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Career Path Section - Matching Left Sidebar Cards */
.right-sidebar .role-progression-container {
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.right-sidebar .role-progression-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
}

.right-sidebar .role-progression-header h3 {
    font-size: 0.95rem;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.right-sidebar .toggle-button {
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    padding: var(--space-xs) var(--space-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.right-sidebar .toggle-button:hover {
    background: var(--primary-gradient);
    color: var(--text-primary);
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

/* Enhanced Org Chart Content - Matching Left Sidebar */
.right-sidebar .org-chart {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.right-sidebar .org-chart .employee {
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: var(--space-xs) var(--space-sm);
    margin: var(--space-xs) 0;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.right-sidebar .org-chart .employee:hover {
    background: var(--primary-gradient);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: var(--shadow-glow);
}

.right-sidebar .org-chart .employee.current {
    background: var(--primary-gradient);
    border-color: var(--border-accent);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
}

/* Enhanced Role Progression Content - Matching Left Sidebar */
.right-sidebar .role-progression-content {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.right-sidebar .role-progression-content .role-item {
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: var(--space-xs) var(--space-sm);
    margin: var(--space-xs) 0;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.right-sidebar .role-progression-content .role-item:hover {
    background: var(--primary-gradient);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: var(--shadow-glow);
}

.right-sidebar .role-progression-content .role-item.current {
    background: var(--primary-gradient);
    border-color: var(--border-accent);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
}

/* Right sidebar hidden state */
.right-sidebar-hidden .right-sidebar {
    transform: translateX(300px);
    width: 0;
    padding: 0;
    overflow: hidden;
    border-left: none;
}

/* Adjust main content when right sidebar is hidden */
.right-sidebar-hidden .main-content {
    margin-right: 0;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .right-sidebar {
        width: 250px;
        padding: 15px;
    }

    .right-sidebar-hidden .right-sidebar {
        transform: translateX(250px);
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 992px) {
    .app-container {
        flex-direction: column;
    }

    .main-content {
        width: 100%;
        min-width: 0;
    }

    /* Tablet Right Sidebar - Similar to mobile */
    .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        right: -100% !important;
        left: auto !important;
        width: 70vw !important;
        max-width: 350px !important;
        height: 100vh !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        z-index: 9999 !important;
        transition: right 0.3s ease-in-out !important;
        overflow-y: auto !important;
        padding: 20px !important;
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Show right sidebar when body has right-sidebar-visible class */
    body.right-sidebar-visible .right-sidebar {
        right: 0 !important;
        transform: translateX(0) !important;
    }

    /* Optimize main content for tablet */
    .messages-container {
        height: calc(100vh - 350px); /* Adjust for header, sidebar, and input area */
    }

    /* Improve input area on tablet */
    .input-area {
        padding: 15px;
    }

    /* Improve preview container on tablet */
    .preview-container {
        max-height: none;
        height: auto;
    }

    /* Make buttons more touch-friendly on tablet */
    button {
        min-height: 40px;
        padding: 8px 16px;
    }

    /* Improve spacing between buttons */
    .preview-actions, .edit-actions {
        gap: 12px;
    }
}

/* Mobile adjustments */
@media (max-width: 992px) {
    .app-container {
        flex-direction: column;
    }

    .main-content {
        width: 100%;
        min-height: 60vh;
    }

    /* Mobile Right Sidebar - Independent Operation */
    .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        right: -100% !important;
        left: auto !important;
        width: 85vw !important;
        max-width: 320px !important;
        height: 100vh !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        z-index: 9999 !important;
        transition: right 0.3s ease-in-out !important;
        overflow-y: auto !important;
        padding: 20px !important;
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Show right sidebar when body has right-sidebar-visible class */
    body.right-sidebar-visible .right-sidebar {
        right: 0 !important;
        transform: translateX(0) !important;
    }

    /* Optimize main content for mobile */
    .messages-container {
        height: calc(100vh - 350px); /* Adjust for header, sidebar, and input area */
    }

    /* Improve input area on mobile */
    .input-area {
        padding: 10px;
    }

    .prompt-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Mobile-specific right toggle alignment override to match left */
@media (max-width: 992px) {
    .right-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transform: none !important;
        margin-left: 0 !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        position: relative !important;
    }
    .right-sidebar-toggle span {
        display: block !important;
        background: white !important;
        width: 20px !important;
        height: 2px !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }
}
/* Force visible position when sidebar active */
body.right-sidebar-visible .right-sidebar {
    right: 0 !important;
    transform: translateX(0) !important;
    visibility: visible !important;
    opacity: 1 !important;
}
/* Small mobile adjustments */
@media (max-width: 480px) {
    .sidebar, .main-content {
        padding: 10px;
    }

    .header {
        padding: 10px;
    }

    /* Adjust sidebar height for small screens */
    .sidebar {
        max-height: 200px;
    }

    .messages-container {
        height: calc(100vh - 300px); /* Adjust for smaller sidebar and screen */
    }

    /* Make buttons more touch-friendly */
    button {
        min-height: 44px; /* Apple's recommended minimum touch target size */
    }

    /* Improve spacing for small screens */
    .preview-actions, .edit-actions {
        flex-direction: column;
        gap: 10px;
    }

    .preview-actions button, .edit-actions button {
        width: 100%;
    }

    /* Ensure mobile right sidebar content is compact */
    .mobile-right-sidebar-content {
        font-size: 0.9em;
    }

    .mobile-right-sidebar-content .org-chart-container,
    .mobile-right-sidebar-content .role-progression-container {
        margin-bottom: 10px;
        padding-bottom: 10px;
    }
}

@media (max-width: 992px) {
    /* Hide zoom indicator overlay that covers sidebar */
    .right-sidebar .zoom-indicator {
        display: none !important;
        pointer-events: none !important;
    }
    /* Remove extra padding/margin on double-click zoom container */
    .right-sidebar .double-click-zoom-container {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
}

/* Remove app container width constraints for full-width layout */
.app-container {
    max-width: none !important;
    width: 100vw !important;
}

/* Ensure main content expands to fill available space */

/* -------------------------------------------------------------------
   Mobile (<768px) Right Sidebar Styles – Match Left Sidebar Layout
   ------------------------------------------------------------------- */
@media (max-width: 768px) {
    /* Copy left sidebar mobile appearance */
    .right-sidebar {
        position: relative !important; /* part of normal flow like left sidebar */
        top: auto !important;
        right: auto !important;
        left: auto !important;
        width: 100% !important;
        max-height: 250px;
        overflow-y: auto;
        padding: 10px;
        transition: max-height 0.3s ease, opacity 0.3s ease;
        border-left: none !important;
        border-bottom: 1px solid #e0e0e0; /* same divider as left */
        box-shadow: none !important;
    }

    /* Hidden (collapsed) state */
    .right-sidebar-hidden .right-sidebar {
        max-height: 0;
        padding: 0;
        overflow: hidden;
        border-bottom: none;
    }

    /* Ensure toggle button is always visible */
    .right-sidebar-toggle {
        display: flex !important;
    }
}

/* Keep previous desktop / tablet rules intact below */
@media (min-width: 993px) {
    .main-content {
        min-width: 0 !important;
        flex: 1;
        width: auto !important;
        max-width: none !important;
    }
}
