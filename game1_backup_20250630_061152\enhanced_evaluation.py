"""
Enhanced Evaluation Module for Rwenzori Innovations Game

This module provides advanced evaluation of user responses using LLM.
It compares responses against task requirements and provides detailed feedback.
"""

import logging
import re
import json
try:
    from .llm_response_generator_openai_format import generate_response_with_llm
except ImportError:
    # Fallback function if the module is not available
    def generate_response_with_llm(prompt, purpose, max_retries=3):
        return f"This is a fallback response for: {prompt[:50]}..."

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Constants
MINIMUM_PROMOTION_SCORE = 7  # Minimum score required for promotion
MINIMUM_COMPLETION_SCORE = 6  # Minimum score required to consider a task completed

def format_requirements(requirements):
    """Format requirements list into a string."""
    if isinstance(requirements, list):
        return "\n".join([f"- {req}" for req in requirements])
    return str(requirements)

def evaluate_response_with_enhanced_llm(user_response, task_description, requirements, task_id):
    """
    Evaluate a user's response using the LLM.

    Args:
        user_response (str): The user's response to evaluate
        task_description (str): Description of the task
        requirements (list or str): Requirements for the task
        task_id (str): Identifier for the task

    Returns:
        dict: Evaluation results including scores and feedback
    """
    print(f"\n\nEnhanced evaluation called for task {task_id}\n\n")
    print(f"\n\nUser response: {user_response[:100]}...\n\n")
    print(f"\n\nTask description: {task_description[:100]}...\n\n")
    print(f"\n\nRequirements: {requirements}\n\n")
    # Construct the evaluation prompt
    formatted_requirements = format_requirements(requirements)

    evaluation_prompt = f"""
You are evaluating a user's response to a task at Rwenzori Innovations.

Task: {task_description}

Requirements:
{formatted_requirements}

User's Response:
{user_response}

Evaluate this response on the following criteria:
1. Completeness (1-10): Does it address all requirements?
2. Quality (1-10): Is the content well-reasoned and appropriate?
3. Professionalism (1-10): Is the tone and language professional?
4. Relevance (1-10): Is the response relevant to the task?

For each criterion, provide:
- A score from 1-10
- Specific feedback explaining the score
- Examples of what's missing or could be improved

Then provide an overall assessment:
- Overall score (1-10)
- Whether this response meets the minimum requirements (Yes/No)
- A final evaluation summary

Format your response as a JSON object with the following structure:
{{
  "completeness": {{
    "score": <score>,
    "feedback": "<feedback>"
  }},
  "quality": {{
    "score": <score>,
    "feedback": "<feedback>"
  }},
  "professionalism": {{
    "score": <score>,
    "feedback": "<feedback>"
  }},
  "relevance": {{
    "score": <score>,
    "feedback": "<feedback>"
  }},
  "overall": {{
    "score": <score>,
    "meets_requirements": <true/false>,
    "summary": "<summary>"
  }},
  "improvement_suggestions": [
    "<suggestion1>",
    "<suggestion2>",
    ...
  ]
}}
"""

    logging.info(f"Sending evaluation prompt for task {task_id}")

    # Call the LLM with the evaluation prompt
    try:
        evaluation_response = generate_response_with_llm(evaluation_prompt, "evaluation")
        logging.info(f"Received evaluation response for task {task_id}")

        # Parse the LLM response to extract scores and feedback
        parsed_results = parse_evaluation_response(evaluation_response)
        return parsed_results
    except Exception as e:
        logging.error(f"Error in evaluation: {str(e)}")
        # Return a default evaluation in case of error
        return create_default_evaluation()

def parse_evaluation_response(evaluation_text):
    """
    Parse the evaluation response from the LLM.

    Args:
        evaluation_text (str): The LLM's evaluation response

    Returns:
        dict: Parsed evaluation results
    """
    try:
        # Try to extract JSON from the response
        json_match = re.search(r'({[\s\S]*})', evaluation_text)
        if json_match:
            json_str = json_match.group(1)
            try:
                # Parse the JSON
                evaluation_data = json.loads(json_str)

                # Extract the required fields
                results = {
                    "completeness_score": evaluation_data.get("completeness", {}).get("score", 5),
                    "quality_score": evaluation_data.get("quality", {}).get("score", 5),
                    "professionalism_score": evaluation_data.get("professionalism", {}).get("score", 5),
                    "relevance_score": evaluation_data.get("relevance", {}).get("score", 5),
                    "overall_score": evaluation_data.get("overall", {}).get("score", 5),
                    "meets_requirements": evaluation_data.get("overall", {}).get("meets_requirements", False) or evaluation_data.get("overall", {}).get("score", 0) >= 9,
                    "feedback": evaluation_data.get("overall", {}).get("summary", ""),
                    "improvement_suggestions": evaluation_data.get("improvement_suggestions", []),
                    "creativity_noted": "creative" in evaluation_text.lower() or "innovative" in evaluation_text.lower()
                }

                # Add detailed feedback
                results["completeness_feedback"] = evaluation_data.get("completeness", {}).get("feedback", "")
                results["quality_feedback"] = evaluation_data.get("quality", {}).get("feedback", "")
                results["professionalism_feedback"] = evaluation_data.get("professionalism", {}).get("feedback", "")
                results["relevance_feedback"] = evaluation_data.get("relevance", {}).get("feedback", "")

                return results
            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse JSON from evaluation: {str(e)}")

        # If JSON parsing fails, fall back to regex-based extraction
        return extract_evaluation_with_regex(evaluation_text)
    except Exception as e:
        logging.error(f"Error parsing evaluation response: {str(e)}")
        return create_default_evaluation()

def extract_evaluation_with_regex(evaluation_text):
    """
    Extract evaluation data using regex as a fallback method.

    Args:
        evaluation_text (str): The LLM's evaluation response

    Returns:
        dict: Extracted evaluation results
    """
    # Extract scores using regex
    completeness_score = extract_score(evaluation_text, "Completeness")
    quality_score = extract_score(evaluation_text, "Quality")
    professionalism_score = extract_score(evaluation_text, "Professionalism")
    relevance_score = extract_score(evaluation_text, "Relevance")
    overall_score = extract_score(evaluation_text, "Overall score")

    # Extract meets requirements
    meets_requirements = "yes" in extract_text(evaluation_text, "meets the minimum requirements").lower() or overall_score >= 9

    # Extract feedback
    feedback = extract_text(evaluation_text, "final evaluation summary")
    if not feedback:
        feedback = extract_text(evaluation_text, "overall assessment")

    # Extract improvement suggestions
    suggestions = []
    suggestion_pattern = r"(?:could be improved|suggestion|improvement).*?:\s*(.*?)(?:\n|$)"
    suggestion_matches = re.finditer(suggestion_pattern, evaluation_text, re.IGNORECASE)
    for match in suggestion_matches:
        if match.group(1).strip():
            suggestions.append(match.group(1).strip())

    # Create results dictionary
    results = {
        "completeness_score": completeness_score,
        "quality_score": quality_score,
        "professionalism_score": professionalism_score,
        "relevance_score": relevance_score,
        "overall_score": overall_score,
        "meets_requirements": meets_requirements,
        "feedback": feedback,
        "improvement_suggestions": suggestions,
        "creativity_noted": "creative" in evaluation_text.lower() or "innovative" in evaluation_text.lower()
    }

    return results

def extract_score(text, criterion):
    """
    Extract a score for a specific criterion from the text.

    Args:
        text (str): The text to search in
        criterion (str): The criterion to find the score for

    Returns:
        int: The extracted score (default: 5)
    """
    pattern = rf"{criterion}.*?(\d+)(?:/10)?"
    match = re.search(pattern, text, re.IGNORECASE)
    if match:
        try:
            score = int(match.group(1))
            # Ensure score is within valid range
            return max(1, min(10, score))
        except ValueError:
            pass
    return 5  # Default score

def extract_text(text, section_name):
    """
    Extract text for a specific section from the evaluation.

    Args:
        text (str): The text to search in
        section_name (str): The section name to find

    Returns:
        str: The extracted text
    """
    pattern = rf"{section_name}.*?:(.*?)(?:\n\n|\Z)"
    match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
    if match:
        return match.group(1).strip()
    return ""

def create_default_evaluation():
    """
    Create a default evaluation in case of errors.

    Returns:
        dict: Default evaluation results
    """
    return {
        "completeness_score": 5,
        "quality_score": 5,
        "professionalism_score": 5,
        "relevance_score": 5,
        "overall_score": 5,
        "meets_requirements": True,  # Default to True to avoid blocking progression
        "feedback": "Unable to evaluate the response. Please try again.",
        "improvement_suggestions": ["Please provide a more detailed response."],
        "creativity_noted": False
    }

def calculate_score(evaluation_results):
    """
    Calculate the final score based on evaluation results.

    Args:
        evaluation_results (dict): The evaluation results

    Returns:
        int: The calculated score
    """
    base_score = 0
    bonus_score = 0

    # Base score from meeting requirements
    if evaluation_results["meets_requirements"]:
        base_score = 10
    else:
        # Partial credit based on completeness score
        base_score = evaluation_results["completeness_score"]

    # Bonus points for exceptional quality
    if evaluation_results["overall_score"] >= 9:
        bonus_score += 5
    elif evaluation_results["overall_score"] >= 7:
        bonus_score += 2

    # Additional bonuses for specific achievements
    if evaluation_results["creativity_noted"]:
        bonus_score += 3

    final_score = base_score + bonus_score
    print(f"\n\nCalculated score: base_score={base_score}, bonus_score={bonus_score}, final_score={final_score}\n\n")
    return final_score

def generate_improvement_feedback(evaluation_results):
    """
    Generate feedback for improvement based on evaluation results.

    Args:
        evaluation_results (dict): The evaluation results

    Returns:
        str: Formatted feedback for improvement
    """
    feedback = "Your response needs improvement in the following areas:\n\n"

    # Add feedback for each criterion if score is below threshold
    if evaluation_results["completeness_score"] < 7:
        feedback += f"**Completeness (Score: {evaluation_results['completeness_score']}/10):**\n"
        feedback += f"{evaluation_results.get('completeness_feedback', 'Your response is missing some required elements.')}\n\n"

    if evaluation_results["quality_score"] < 7:
        feedback += f"**Quality (Score: {evaluation_results['quality_score']}/10):**\n"
        feedback += f"{evaluation_results.get('quality_feedback', 'The quality of your response could be improved.')}\n\n"

    if evaluation_results["professionalism_score"] < 7:
        feedback += f"**Professionalism (Score: {evaluation_results['professionalism_score']}/10):**\n"
        feedback += f"{evaluation_results.get('professionalism_feedback', 'Your response could be more professional.')}\n\n"

    if evaluation_results["relevance_score"] < 7:
        feedback += f"**Relevance (Score: {evaluation_results['relevance_score']}/10):**\n"
        feedback += f"{evaluation_results.get('relevance_feedback', 'Your response could be more relevant to the task.')}\n\n"

    # Add overall feedback
    feedback += f"**Overall Assessment (Score: {evaluation_results['overall_score']}/10):**\n"
    feedback += f"{evaluation_results['feedback']}\n\n"

    # Add improvement suggestions
    if evaluation_results["improvement_suggestions"]:
        feedback += "**Suggestions for Improvement:**\n"
        for i, suggestion in enumerate(evaluation_results["improvement_suggestions"], 1):
            feedback += f"{i}. {suggestion}\n"

    return feedback

def update_role_average_score(game_state, new_score):
    """
    Update the average score for the current role.

    Args:
        game_state (dict): The current game state
        new_score (int): The new score to incorporate

    Returns:
        float: The updated average score
    """
    # Get current values
    current_avg = game_state.get("role_average_score", 0)
    completed_tasks = game_state.get("role_challenges_completed", 0)

    # Calculate new average
    if completed_tasks == 0:
        new_avg = new_score
    else:
        # Weighted average calculation
        total = current_avg * completed_tasks
        new_avg = (total + new_score) / (completed_tasks + 1)

    # Update game state
    game_state["role_average_score"] = new_avg

    return new_avg
