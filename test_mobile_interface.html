<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Mobile Interface Test - Corporate Prompt Master</title>
    <style>
        /* Basic test styles */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #121212;
            color: #ffffff;
        }

        .test-container {
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }

        .test-section {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #444444;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #5a67d8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin: 8px 0;
            min-height: 44px;
        }

        .test-button:active {
            transform: scale(0.95);
        }

        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 8px 0;
            font-size: 14px;
        }

        .status.success {
            background: #4facfe;
            color: white;
        }

        .status.error {
            background: #ff9a9e;
            color: white;
        }

        .status.info {
            background: #f0f2f5;
            color: #333;
        }

        @media (max-width: 992px) {
            .mobile-test {
                display: block;
            }
            .desktop-test {
                display: none;
            }
        }

        @media (min-width: 993px) {
            .mobile-test {
                display: none;
            }
            .desktop-test {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1>Mobile Interface Test</h1>
            <p>This page tests the mobile interface detection and basic functionality.</p>
        </div>

        <div class="test-section mobile-test">
            <h2>✅ Mobile Mode Active</h2>
            <p>You are viewing this on a mobile device (width ≤ 992px)</p>
            <div class="status success">Mobile interface should be visible</div>
        </div>

        <div class="test-section desktop-test">
            <h2>🖥️ Desktop Mode Active</h2>
            <p>You are viewing this on a desktop device (width > 992px)</p>
            <div class="status info">Desktop interface should be visible</div>
        </div>

        <div class="test-section">
            <h3>Device Information</h3>
            <div id="device-info">
                <div>Screen Width: <span id="screen-width"></span>px</div>
                <div>Window Width: <span id="window-width"></span>px</div>
                <div>Device Pixel Ratio: <span id="pixel-ratio"></span></div>
                <div>User Agent: <span id="user-agent"></span></div>
                <div>Touch Support: <span id="touch-support"></span></div>
            </div>
        </div>

        <div class="test-section">
            <h3>Touch Tests</h3>
            <button class="test-button" id="touch-test-btn">Test Touch Response</button>
            <div id="touch-result" class="status info">Tap the button above to test touch response</div>
        </div>

        <div class="test-section">
            <h3>Viewport Tests</h3>
            <button class="test-button" id="viewport-test-btn">Test Viewport Units</button>
            <div id="viewport-result" class="status info">Test viewport height and width units</div>
        </div>

        <div class="test-section">
            <h3>CSS Variables Test</h3>
            <div style="background: var(--primary-color, #667eea); padding: 12px; border-radius: 8px; color: white; margin: 8px 0;">
                Primary Color Variable Test
            </div>
            <div style="background: var(--success-color, #4facfe); padding: 12px; border-radius: 8px; color: white; margin: 8px 0;">
                Success Color Variable Test
            </div>
        </div>

        <div class="test-section">
            <h3>Performance Test</h3>
            <button class="test-button" id="performance-test-btn">Test Animation Performance</button>
            <div id="performance-result" class="status info">Test 60fps animations</div>
            <div id="animation-box" style="width: 50px; height: 50px; background: #667eea; border-radius: 8px; margin: 8px 0; transition: transform 0.3s ease;"></div>
        </div>

        <div class="test-section">
            <h3>Sidebar Scrolling Test</h3>
            <div class="test-sidebar-demo" style="max-height: 200px; overflow-y: auto; background: #1e1e1e; border-radius: 8px; padding: 16px; margin: 16px 0; border: 1px solid #444;">
                <h4 style="color: #667eea; margin: 0 0 12px 0;">Demo Scrollable Content</h4>
                <div style="height: 400px; background: linear-gradient(180deg, #667eea 0%, #5a67d8 100%); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                    Scroll to see more content below
                </div>
                <p style="color: white; margin: 16px 0;">This simulates the scrollable sidebar content. In the actual game, you'll see:</p>
                <ul style="color: #ccc; padding-left: 20px;">
                    <li>Character information and stats</li>
                    <li>Game instructions and tips</li>
                    <li>Company hierarchy visualization</li>
                    <li>Career path progression</li>
                    <li>Role requirements and skills</li>
                    <li>Pro tips and help sections</li>
                </ul>
                <div style="height: 200px; background: #2a2a2a; border-radius: 6px; margin: 16px 0; display: flex; align-items: center; justify-content: center; color: #888;">
                    More content continues...
                </div>
            </div>
            <div class="status info">✓ Sidebar content should be scrollable with smooth touch scrolling</div>
        </div>

        <div class="test-section">
            <h3>Game Integration Test</h3>
            <p>To test the complete mobile interface:</p>
            <ol>
                <li>Navigate to <code>/game/</code> on your mobile device</li>
                <li>Check that mobile interface is visible</li>
                <li>Test sidebar toggles in header</li>
                <li>Test swipe gestures</li>
                <li>Test prompt input and buttons</li>
                <li><strong>Test sidebar scrolling:</strong>
                    <ul>
                        <li>Open left sidebar (game info)</li>
                        <li>Scroll through all content sections</li>
                        <li>Open right sidebar (career path)</li>
                        <li>Scroll through org chart and career steps</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Sidebar Content Checklist</h3>
            <h4>Left Sidebar (Game Info) Should Include:</h4>
            <ul style="font-size: 14px; line-height: 1.6;">
                <li>✓ Current Contact (character info)</li>
                <li>✓ Game Stats (role, score, challenges, progress)</li>
                <li>✓ Context Info (manager, task, company)</li>
                <li>✓ How to Play instructions</li>
                <li>✓ Game Controls (continue/restart)</li>
                <li>✓ Progress tracking (if logged in)</li>
                <li>✓ Login/Register prompts (if guest)</li>
                <li>✓ Keyboard shortcuts reference</li>
            </ul>

            <h4>Right Sidebar (Career Path) Should Include:</h4>
            <ul style="font-size: 14px; line-height: 1.6;">
                <li>✓ Company Hierarchy (visual org chart)</li>
                <li>✓ Career Path (step-by-step progression)</li>
                <li>✓ Role Requirements (skills & challenges)</li>
                <li>✓ Company Information (if available)</li>
                <li>✓ Pro Tips for success</li>
                <li>✓ Help & Feedback buttons</li>
            </ul>
        </div>
    </div>

    <script>
        // Device information
        document.getElementById('screen-width').textContent = screen.width;
        document.getElementById('window-width').textContent = window.innerWidth;
        document.getElementById('pixel-ratio').textContent = window.devicePixelRatio;
        document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
        document.getElementById('touch-support').textContent = 'ontouchstart' in window ? 'Yes' : 'No';

        // Touch test
        document.getElementById('touch-test-btn').addEventListener('click', function() {
            const startTime = performance.now();
            this.style.transform = 'scale(0.95)';

            setTimeout(() => {
                this.style.transform = 'scale(1)';
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);

                const result = document.getElementById('touch-result');
                if (responseTime < 100) {
                    result.className = 'status success';
                    result.textContent = `✅ Excellent touch response: ${responseTime}ms`;
                } else if (responseTime < 200) {
                    result.className = 'status info';
                    result.textContent = `⚠️ Good touch response: ${responseTime}ms`;
                } else {
                    result.className = 'status error';
                    result.textContent = `❌ Slow touch response: ${responseTime}ms`;
                }
            }, 100);
        });

        // Viewport test
        document.getElementById('viewport-test-btn').addEventListener('click', function() {
            const vh = window.innerHeight;
            const vw = window.innerWidth;
            const result = document.getElementById('viewport-result');

            result.className = 'status success';
            result.textContent = `✅ Viewport: ${vw}x${vh}px (${vw <= 992 ? 'Mobile' : 'Desktop'})`;
        });

        // Performance test
        document.getElementById('performance-test-btn').addEventListener('click', function() {
            const animationBox = document.getElementById('animation-box');
            const result = document.getElementById('performance-result');

            let frameCount = 0;
            const startTime = performance.now();

            // Animate for 1 second
            const animate = () => {
                frameCount++;
                animationBox.style.transform = `translateX(${Math.sin(frameCount * 0.1) * 100}px)`;

                if (performance.now() - startTime < 1000) {
                    requestAnimationFrame(animate);
                } else {
                    const fps = Math.round(frameCount);
                    animationBox.style.transform = 'translateX(0)';

                    if (fps >= 55) {
                        result.className = 'status success';
                        result.textContent = `✅ Excellent performance: ~${fps} FPS`;
                    } else if (fps >= 30) {
                        result.className = 'status info';
                        result.textContent = `⚠️ Good performance: ~${fps} FPS`;
                    } else {
                        result.className = 'status error';
                        result.textContent = `❌ Poor performance: ~${fps} FPS`;
                    }
                }
            };

            requestAnimationFrame(animate);
        });

        // Update window width on resize
        window.addEventListener('resize', function() {
            document.getElementById('window-width').textContent = window.innerWidth;
        });

        // Log mobile interface status
        console.log('Mobile Interface Test Loaded');
        console.log('Window width:', window.innerWidth);
        console.log('Is mobile:', window.innerWidth <= 992);
        console.log('Touch support:', 'ontouchstart' in window);
    </script>
</body>
</html>
