/* Preview Styles */
.preview-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-container h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.prompt-section, .evaluation-section, .response-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.prompt-section h2, .evaluation-section h2, .response-section h2 {
    margin-bottom: 15px;
    color: #0056b3;
    font-size: 1.5rem;
}

.prompt-text, .response-text {
    padding: 15px;
    background-color: #e6f2ff;
    border-radius: 5px;
    border: 1px solid #b8d4ff;
    min-height: 100px;
    white-space: pre-wrap;
}

.evaluation-summary {
    font-size: 0.95em;
    line-height: 1.5;
    color: #333;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border-left: 3px solid #0056b3;
}

.tips-section h3, .detailed-feedback h3 {
    font-weight: bold;
    color: #0056b3;
    margin: 15px 0 10px;
    font-size: 1.1em;
}

#improvement-tips {
    list-style-type: disc;
    margin-left: 20px;
}

#improvement-tips li {
    margin-bottom: 8px;
    color: #555;
}

.dimensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
}

.dimension-card {
    padding: 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.dimension-name {
    font-weight: bold;
    color: #333;
}

.dimension-score {
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.9em;
}

.dimension-score.high {
    background-color: #d4edda;
    color: #155724;
}

.dimension-score.medium {
    background-color: #fff3cd;
    color: #856404;
}

.dimension-score.low {
    background-color: #f8d7da;
    color: #721c24;
}

.dimension-feedback {
    font-size: 0.9em;
    color: #555;
    margin-bottom: 8px;
}

.dimension-suggestions {
    font-size: 0.85em;
    color: #666;
    font-style: italic;
    padding-left: 10px;
    border-left: 2px solid #0056b3;
}

.actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background-color: #4caf50;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover {
    background-color: #3d9140;
}

.btn-secondary:hover {
    background-color: #5a6268;
}
