"""
Django management command to test the challenge completion tracking fixes.
Run with: python manage.py test_challenge_fixes
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from game.models import GameSession, Message
from game.game_state_manager import process_task_completion, check_for_promotion
from game.role_progression import ROLE_PROGRESSION
import json


class Command(BaseCommand):
    help = 'Test the challenge completion tracking fixes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running tests',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=' * 60))
        self.stdout.write(self.style.SUCCESS('TESTING CHALLENGE COMPLETION TRACKING FIXES'))
        self.stdout.write(self.style.SUCCESS('=' * 60))

        try:
            # Test 1: Challenge counting
            self.test_challenge_counting()

            # Test 2: Role progression
            self.test_role_progression()

            # Test 3: Message creation
            self.test_message_creation()

            # Test 4: End-to-end workflow
            self.test_end_to_end_workflow()

            self.stdout.write(self.style.SUCCESS('\n' + '=' * 60))
            self.stdout.write(self.style.SUCCESS('ALL TESTS PASSED! ✅'))
            self.stdout.write(self.style.SUCCESS('The challenge completion tracking fixes are working correctly.'))
            self.stdout.write(self.style.SUCCESS('=' * 60))

            if options['cleanup']:
                self.cleanup_test_data()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'\n❌ TEST FAILED: {str(e)}'))
            import traceback
            self.stdout.write(self.style.ERROR(traceback.format_exc()))

    def test_challenge_counting(self):
        """Test that challenges are counted correctly without double-counting"""
        self.stdout.write('\n1. Testing challenge counting...')

        # Create test user and game session
        user, created = User.objects.get_or_create(username='test_challenge_counting')
        game_session, created = GameSession.objects.get_or_create(
            user=user,
            defaults={
                'current_role': 'applicant',
                'challenges_completed': 0,
                'role_challenges_completed': 0,
                'performance_score': 0
            }
        )

        # Reset to known state
        game_session.challenges_completed = 0
        game_session.role_challenges_completed = 0
        game_session.performance_score = 0
        game_session.save()

        initial_challenges = game_session.challenges_completed
        initial_role_challenges = game_session.role_challenges_completed

        self.stdout.write(f'   Initial state: challenges={initial_challenges}, role_challenges={initial_role_challenges}')

        # Simulate task completion
        game_state = game_session.to_dict()
        evaluation_results = {"overall_score": 85, "meets_requirements": True}
        points_earned = 10

        # Process task completion
        updated_game_state = process_task_completion(game_state, evaluation_results, points_earned)

        # Update game session with results
        game_session.challenges_completed = updated_game_state['challenges_completed']
        game_session.role_challenges_completed = updated_game_state['role_challenges_completed']
        game_session.performance_score = updated_game_state['performance_score']
        game_session.save()

        self.stdout.write(f'   After completion: challenges={game_session.challenges_completed}, role_challenges={game_session.role_challenges_completed}')

        # Verify correct increments
        assert game_session.challenges_completed == initial_challenges + 1, f"Expected {initial_challenges + 1}, got {game_session.challenges_completed}"

        # Note: role_challenges_completed may be reset to 0 if promotion occurred
        # For applicant role (which needs only 1 challenge), completing 1 task triggers promotion
        if game_session.current_role == 'junior_assistant':
            # User was promoted from applicant to junior_assistant, so role_challenges_completed should be reset to 0
            assert game_session.role_challenges_completed == 0, f"Expected 0 after promotion, got {game_session.role_challenges_completed}"
            self.stdout.write(f'   ✅ User was promoted from applicant to junior_assistant! Role challenges correctly reset to 0')
        else:
            # No promotion occurred, role_challenges_completed should be incremented
            assert game_session.role_challenges_completed == initial_role_challenges + 1, f"Expected {initial_role_challenges + 1}, got {game_session.role_challenges_completed}"
            self.stdout.write(f'   ✅ No promotion occurred, role challenges incremented correctly')

        self.stdout.write(self.style.SUCCESS('   ✅ Challenge counting test passed!'))

        # Test case 2: Test a role that doesn't get promoted (junior_assistant needs 3 challenges)
        self.stdout.write('\n   Testing challenge counting without promotion...')

        # Create another test user for junior_assistant role
        user2, created = User.objects.get_or_create(username='test_challenge_no_promotion')
        game_session2, created = GameSession.objects.get_or_create(
            user=user2,
            defaults={
                'current_role': 'junior_assistant',
                'challenges_completed': 0,
                'role_challenges_completed': 0,
                'performance_score': 0
            }
        )

        # Reset to known state
        game_session2.current_role = 'junior_assistant'
        game_session2.challenges_completed = 0
        game_session2.role_challenges_completed = 0
        game_session2.performance_score = 0
        game_session2.save()

        initial_role = game_session2.current_role

        # Complete one task (should not trigger promotion since junior_assistant needs 3)
        game_state2 = game_session2.to_dict()
        updated_game_state2 = process_task_completion(game_state2, evaluation_results, points_earned)

        game_session2.challenges_completed = updated_game_state2['challenges_completed']
        game_session2.role_challenges_completed = updated_game_state2['role_challenges_completed']
        game_session2.current_role = updated_game_state2['current_role']
        game_session2.save()

        self.stdout.write(f'   Junior assistant after 1 task: role={game_session2.current_role}, role_challenges={game_session2.role_challenges_completed}')

        # Verify no promotion occurred and challenges incremented correctly
        assert game_session2.current_role == initial_role, f"Expected no role change, but role changed from {initial_role} to {game_session2.current_role}"
        assert game_session2.challenges_completed == 1, f"Expected 1 total challenge, got {game_session2.challenges_completed}"
        assert game_session2.role_challenges_completed == 1, f"Expected 1 role challenge, got {game_session2.role_challenges_completed}"

        self.stdout.write(self.style.SUCCESS('   ✅ No-promotion challenge counting test passed!'))

    def test_role_progression(self):
        """Test that role progression works correctly"""
        self.stdout.write('\n2. Testing role progression...')

        # Test applicant promotion (needs 1 challenge)
        game_state = {
            'current_role': 'applicant',
            'challenges_completed': 1,
            'role_challenges_completed': 1,
            'performance_score': 10,
            'completed_roles': [],
            'role_average_score': 85
        }

        self.stdout.write(f'   Before promotion: role={game_state["current_role"]}, role_challenges={game_state["role_challenges_completed"]}')

        promoted, promotion_message = check_for_promotion(game_state, ROLE_PROGRESSION)

        self.stdout.write(f'   Promotion result: promoted={promoted}')
        if promoted:
            self.stdout.write(f'   New role: {game_state["current_role"]}')
            self.stdout.write(f'   Role challenges reset to: {game_state["role_challenges_completed"]}')
            self.stdout.write(f'   Completed roles: {game_state["completed_roles"]}')

        # Verify promotion
        assert promoted == True, "Expected promotion to occur"
        assert game_state['current_role'] == 'junior_assistant', f"Expected junior_assistant, got {game_state['current_role']}"
        assert game_state['role_challenges_completed'] == 0, f"Expected 0, got {game_state['role_challenges_completed']}"
        assert 'applicant' in game_state['completed_roles'], "Expected applicant in completed roles"

        self.stdout.write(self.style.SUCCESS('   ✅ Role progression test passed!'))

    def test_message_creation(self):
        """Test that promotion messages are created correctly"""
        self.stdout.write('\n3. Testing message creation...')

        # Create test user and game session
        user, created = User.objects.get_or_create(username='test_message_creation')
        game_session, created = GameSession.objects.get_or_create(
            user=user,
            defaults={
                'current_role': 'junior_assistant',
                'challenges_completed': 1,
                'role_challenges_completed': 0,
                'performance_score': 10
            }
        )

        # Clear existing messages
        Message.objects.filter(game_session=game_session).delete()

        initial_message_count = Message.objects.filter(game_session=game_session).count()
        self.stdout.write(f'   Initial message count: {initial_message_count}')

        # Create a promotion message
        Message.objects.create(
            game_session=game_session,
            message_id="test_promotion_msg",
            sender="ceo",
            text="Congratulations! You've been promoted!",
            html="<p>Congratulations! You've been promoted!</p>",
            is_challenge=False,
            is_markdown=True,
            is_promotion=True
        )

        final_message_count = Message.objects.filter(game_session=game_session).count()
        promotion_messages = Message.objects.filter(game_session=game_session, is_promotion=True).count()

        self.stdout.write(f'   Final message count: {final_message_count}')
        self.stdout.write(f'   Promotion messages: {promotion_messages}')

        # Verify message creation
        assert final_message_count == initial_message_count + 1, "Expected exactly one new message"
        assert promotion_messages == 1, "Expected exactly one promotion message"

        self.stdout.write(self.style.SUCCESS('   ✅ Message creation test passed!'))

    def test_end_to_end_workflow(self):
        """Test the complete workflow from task completion to promotion"""
        self.stdout.write('\n4. Testing end-to-end workflow...')

        # Create test user and game session
        user, created = User.objects.get_or_create(username='test_end_to_end')
        game_session, created = GameSession.objects.get_or_create(
            user=user,
            defaults={
                'current_role': 'applicant',
                'challenges_completed': 0,
                'role_challenges_completed': 0,
                'performance_score': 0,
                'completed_roles': '[]'
            }
        )

        # Reset to known state
        game_session.current_role = 'applicant'
        game_session.challenges_completed = 0
        game_session.role_challenges_completed = 0
        game_session.performance_score = 0
        game_session.completed_roles = '[]'
        game_session.save()

        # Clear existing messages
        Message.objects.filter(game_session=game_session).delete()

        self.stdout.write(f'   Starting state: role={game_session.current_role}, challenges={game_session.challenges_completed}')

        # Simulate completing the cover letter task (applicant needs 1 task to advance)
        game_state = game_session.to_dict()
        evaluation_results = {"overall_score": 90, "meets_requirements": True}
        points_earned = 10

        # Process task completion
        updated_game_state = process_task_completion(game_state, evaluation_results, points_earned)

        # Update game session
        game_session.current_role = updated_game_state['current_role']
        game_session.challenges_completed = updated_game_state['challenges_completed']
        game_session.role_challenges_completed = updated_game_state['role_challenges_completed']
        game_session.performance_score = updated_game_state['performance_score']
        game_session.set_completed_roles(updated_game_state['completed_roles'])
        game_session.save()

        self.stdout.write(f'   After task completion: role={game_session.current_role}, challenges={game_session.challenges_completed}')
        self.stdout.write(f'   Role challenges: {game_session.role_challenges_completed}')
        self.stdout.write(f'   Completed roles: {game_session.get_completed_roles()}')

        # Verify the workflow
        assert game_session.challenges_completed == 1, f"Expected 1 challenge completed, got {game_session.challenges_completed}"
        assert game_session.current_role == 'junior_assistant', f"Expected promotion to junior_assistant, got {game_session.current_role}"
        assert game_session.role_challenges_completed == 0, f"Expected role challenges reset to 0, got {game_session.role_challenges_completed}"
        assert 'applicant' in game_session.get_completed_roles(), "Expected applicant in completed roles"

        # Test that promotion message would be created (simulate views.py behavior)
        if updated_game_state.get('promoted', False) or updated_game_state.get('promotion_message'):
            Message.objects.create(
                game_session=game_session,
                message_id="test_end_to_end_promotion",
                sender="ceo",
                text="Congratulations! You've been promoted to Junior Assistant!",
                html="<p>Congratulations! You've been promoted to Junior Assistant!</p>",
                is_challenge=False,
                is_markdown=True,
                is_promotion=True
            )

        promotion_messages = Message.objects.filter(game_session=game_session, is_promotion=True).count()
        self.stdout.write(f'   Promotion messages created: {promotion_messages}')

        self.stdout.write(self.style.SUCCESS('   ✅ End-to-end workflow test passed!'))

    def cleanup_test_data(self):
        """Clean up test data"""
        self.stdout.write('\nCleaning up test data...')

        test_users = ['test_challenge_counting', 'test_challenge_no_promotion', 'test_message_creation', 'test_end_to_end']
        for username in test_users:
            try:
                user = User.objects.get(username=username)
                # Delete associated game sessions and messages
                GameSession.objects.filter(user=user).delete()
                user.delete()
                self.stdout.write(f'   Deleted test user: {username}')
            except User.DoesNotExist:
                pass

        self.stdout.write(self.style.SUCCESS('   ✅ Test data cleaned up!'))
