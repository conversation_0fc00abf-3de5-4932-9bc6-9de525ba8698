{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="theme-color" content="#667eea">
    <meta name="color-scheme" content="dark light">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicons/favicon.ico' %}">
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicons/favicon.svg' %}">
    <link rel="apple-touch-icon" href="{% static 'img/favicons/favicon.ico' %}">
    <!-- Always use dark mode -->
    <style>
        /* Force dark mode by default */
        html, body {
            background-color: #121212;
            color: #ffffff;
        }
        html.dark-mode, body.dark-mode,
        html, body {
            --text-color: #ffffff;
            --background-color: #121212;
            --card-bg: #1e1e1e;
            --sidebar-bg: #1e1e1e;
            --header-bg: #1a1a1a;
            --border-color: #444444;
            --input-bg: #2a2a2a;
            --input-border: #555555;
        }
    </style>
    <script>
        (function() {
            // Always apply dark mode immediately before DOM is fully loaded
            document.documentElement.classList.add('dark-mode');
        })();
    </script>
    <title>Corporate Prompt Master - Context Aware Edition</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for navbar icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{% static 'game/css/context_aware_styles.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/org-chart-styles.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/role_progression_updated.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/celebration.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/manager_feedback_styles.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/company-hierarchy.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/responsive-preview.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/right-sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/chart-hover-zoom.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/hide-right-toggle.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/dark-light-mode.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/dark-feedback-styles.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/promotion-styles.css' %}">
    <link rel="stylesheet" href="{% static 'game/css/navbar-hover-header.css' %}">
    <!-- Professional Mobile Interface CSS -->
    <link rel="stylesheet" href="{% static 'game/css/mobile-professional.css' %}">
    <style>
        /* Ensure page scrolling */
        html, body {
            height: auto;
            min-height: 100%;
            margin: 0;
            padding: 0;
            overflow-y: auto;
        }

        /* Offline mode alert styling */
        #offline-mode-alert .modal-content {
            border-left: 5px solid #dc3545;
            max-width: 500px;
        }

        #offline-mode-alert h2 {
            color: #dc3545;
        }

        /* Mobile responsiveness removed - desktop only design */

        /* Additional styles for markdown content */
        .message-text h1, .message-text h2, .message-text h3 {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
        }
        .message-text ul, .message-text ol {
            margin-left: 1.5em;
        }
        .message-text blockquote {
            border-left: 3px solid var(--blockquote-border, #ccc);
            margin-left: 0.5em;
            padding-left: 0.5em;
            color: var(--blockquote-color, #666);
        }
        .message-text code {
            background-color: var(--code-bg, #f0f0f0);
            color: var(--text-color, #333);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
        }
        .message-text pre {
            background-color: var(--code-bg, #f0f0f0);
            color: var(--text-color, #333);
            padding: 0.5em;
            border-radius: 3px;
            overflow-x: auto;
        }
        .message-text pre code {
            background-color: transparent;
        }
        .message-text hr {
            border: none;
            border-top: 1px solid var(--sidebar-border, #ccc);
            margin: 1em 0;
        }
        .message-text table {
            border-collapse: collapse;
            width: 100%;
        }
        .message-text th, .message-text td {
            border: 1px solid var(--sidebar-border, #ccc);
            padding: 0.5em;
            color: var(--text-color, #333);
        }
        .message-text th {
            background-color: var(--code-bg, #f0f0f0);
        }
        .message-text img {
            max-width: 100%;
        }
        .message-text a {
            color: var(--context-label-color, #0066cc);
            text-decoration: none;
        }
        .message-text a:hover {
            text-decoration: underline;
        }
        .message-text strong, .message-text b {
            font-weight: bold;
        }
        .message-text em, .message-text i {
            font-style: italic;
        }

        /* Context awareness indicators */
        .context-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            font-size: 0.9em;
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }
        .context-info div {
            margin-bottom: 5px;
        }
        .context-label {
            font-weight: bold;
            color: #555;
            transition: color 0.3s ease;
        }

        /* Company badge in navbar */
        .company-badge {
            display: inline-block;
            margin-left: 10px;
            padding: 2px 8px;
            background-color: var(--bs-primary, #0d6efd);
            color: white;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: normal;
            vertical-align: middle;
        }

        /* Company info in sidebar */
        .company-info, .team-info {
            margin-top: 5px;
            padding-top: 5px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Enhanced evaluation styles */
        .enhanced-evaluation-container {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #4a86e8;
            transition: background-color 0.3s ease, border-left-color 0.3s ease;
        }
        .enhanced-evaluation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .enhanced-evaluation-label {
            font-weight: bold;
            color: #333;
        }
        .meets-requirements-indicator {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .meets-requirements-indicator.yes {
            background-color: #d4edda;
            color: #155724;
        }
        .meets-requirements-indicator.no {
            background-color: #f8d7da;
            color: #721c24;
        }
        .improvement-feedback {
            font-size: 0.9em;
            line-height: 1.5;
            white-space: pre-line;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            overflow-y: visible;
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* Game-specific header adjustments */
        .game-header {
            position: fixed;
            top: 0; /* Always visible at the top */
            left: 0;
            right: 0;
            z-index: 9999;
            width: 100%;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            background-color: #0f1117;
        }

        /* Adjust the app container to account for the fixed header */
        .app-container {
            padding-top: 56px; /* Height of the header */
            position: relative;
            z-index: 1;
        }

        /* Company badge styling to match unified header */
        .company-badge {
            font-size: 0.8rem;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            margin-left: 0.5rem;
        }

        /* Help button styling */
        .help-button {
            background-color: rgba(0, 123, 255, 0.2);
            border-radius: 0.25rem;
            padding: 0.3rem 0.7rem;
            margin-left: 0.5rem;
            transition: background-color 0.3s ease;
        }

        .help-button:hover {
            background-color: rgba(0, 123, 255, 0.4);
        }

        /* Ensure the hover header is hidden by default */
        .hover-header {
            top: -60px;
            z-index: 9998; /* Just below the game header */
        }

        /* Game controls container styling */
        .game-controls-container {
            padding: 5px;
            margin-bottom: 10px;
        }

        /* Restart game button styling */
        #restart-game-button-main {
            background-color: rgba(220, 53, 69, 0.1);
            color: #ff6b6b;
            border-color: #ff6b6b;
            transition: all 0.3s ease;
        }

        #restart-game-button-main:hover {
            background-color: #dc3545;
            color: #ffffff;
            border-color: #dc3545;
        }

        /* Dark mode specific adjustments */
        body.dark-mode .game-header .navbar {
            background-color: #1a1a2e !important;
        }

        /* Company badge styling */
        .game-header .company-badge {
            font-size: 0.8rem;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            margin-left: 0.5rem;
        }

        /* Dropdown menu styling for dark mode */
        body.dark-mode .game-header .dropdown-menu {
            background-color: #252538;
            border-color: #2a2a45;
        }

        body.dark-mode .game-header .dropdown-item {
            color: #e0e0e0;
        }

        body.dark-mode .game-header .dropdown-item:hover {
            background-color: #2a2a45;
        }

        body.dark-mode .game-header .dropdown-divider {
            border-color: #2a2a45;
        }

        /* Ensure the sidebar is visible and positioned correctly */
        .sidebar {
            z-index: 10;
            position: relative;
        }

        /* Ensure the main content area is positioned correctly */
        .main-content {
            z-index: 10;
            position: relative;
        }

        /* Animation for the header when it appears */
        @keyframes headerFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .game-header.visible {
            animation: headerFadeIn 0.3s ease-out;
        }

        /* Floating help button */
        .floating-help-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #0d6efd;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .floating-help-btn:hover {
            transform: scale(1.1);
            background-color: #0b5ed7;
            color: white;
            text-decoration: none;
        }

        .floating-help-btn:focus {
            outline: none;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body class="dark-mode{% if is_anonymous %} anonymous-user{% endif %}">
    {% csrf_token %}

    <!-- Game Header -->
    <header id="game-header" class="game-header">
        <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #0f1117; border-bottom: 1px solid #1e2433;">
            <div class="container-fluid">
                <a class="navbar-brand" href="{% url 'corporate:home' %}">
                    <i class="fas fa-building me-2"></i>Corporate Prompt Master
                    {% if company %}
                    <span class="company-badge">{{ company.name }}</span>
                    {% endif %}
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#gameNavbar">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Standalone Help Button (visible even when navbar is collapsed) -->
                <a class="btn btn-outline-light btn-sm ms-2 d-none d-md-inline-block" href="{% url 'game:help_redirect' %}" target="_blank">
                    <i class="fas fa-question-circle me-1"></i>Help
                </a>

                <!-- Mobile Help Button (only visible on small screens) -->
                <a class="btn btn-primary btn-sm ms-2 d-inline-block d-md-none" href="{% url 'game:help_redirect' %}" target="_blank">
                    <i class="fas fa-question-circle"></i>
                </a>

                <div class="collapse navbar-collapse" id="gameNavbar">
                    <!-- Main Navigation Links -->
                    <ul class="navbar-nav me-auto">
                        <!-- Home Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:home' %}">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>

                        <!-- Games Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:game_list' %}">
                                <i class="fas fa-gamepad me-1"></i>Games
                            </a>
                        </li>

                        <!-- Leaderboard Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:leaderboard' %}">
                                <i class="fas fa-trophy me-1"></i>Leaderboard
                            </a>
                        </li>

                        {% if user.is_authenticated %}
                        <!-- Dashboard Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:corporate_dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>

                        <!-- Certificates Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:view_certificates' %}">
                                <i class="fas fa-certificate me-1"></i>Certificates
                            </a>
                        </li>

                        {% if user.is_superuser %}
                        <!-- Superadmin Link -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'superadmin:dashboard' %}">
                                <i class="fas fa-shield-alt me-1"></i>Superadmin
                            </a>
                        </li>
                        {% endif %}
                        {% endif %}

                        <!-- Help Link -->
                        <li class="nav-item">
                            <a class="nav-link help-button" href="{% url 'game:help_redirect' %}" target="_blank">
                                <i class="fas fa-question-circle me-1"></i>Help
                            </a>
                        </li>
                    </ul>

                    <!-- User Menu -->
                    <ul class="navbar-nav">
                        {% if user.is_authenticated %}
                        <!-- User Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>{{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="{% url 'corporate:corporate_dashboard' %}">
                                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'corporate:edit_profile' %}">
                                        <i class="fas fa-user-edit me-1"></i>Edit Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'corporate:corporate_register' %}">
                                        <i class="fas fa-plus-circle me-1"></i>Create Company
                                    </a>
                                </li>
                                {% if user.is_superuser %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'superadmin:dashboard' %}">
                                        <i class="fas fa-shield-alt me-1"></i>Superadmin Dashboard
                                    </a>
                                </li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'corporate:corporate_logout' %}">
                                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% else %}
                        <!-- Login/Register Links -->
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:corporate_login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'corporate:corporate_register' %}">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hover Header (Navbar Style) - Hidden but kept for JavaScript compatibility -->
    <div id="hover-header" class="hover-header" style="display: none;">
        <!-- Empty but kept for JavaScript compatibility -->
    </div>

    <!-- Floating Help Button -->
    <a href="{% url 'game:help_redirect' %}" class="floating-help-btn" target="_blank" title="Get Help">
        <i class="fas fa-question-circle"></i>
    </a>

    <div class="app-container">
        <!-- Debug Button (hidden by default) -->
        <div id="debug-button" style="position: fixed; top: 10px; right: 10px; z-index: 9999; display: none;">
            <button onclick="toggleDebugPanel()" style="background-color: #333; color: #00ff00; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-family: monospace;">
                Debug
            </button>
        </div>

        {% if company and not game_accessible %}
        <!-- Company Access Restricted Message -->
        <div class="access-restricted-container" style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 80vh; text-align: center; padding: 20px;">
            <div class="access-restricted-icon" style="font-size: 4rem; margin-bottom: 20px;">
                <i class="fas fa-lock"></i>
            </div>
            <h2>Access Restricted</h2>
            <p>This company game is restricted to authorized users only.</p>
            {% if company_settings.require_login %}
            <p>Please log in to access this game.</p>
            <div style="margin-top: 20px;">
                <a href="/corporate/login/" class="primary-button" style="text-decoration: none; display: inline-block; margin: 0 10px;">Log In</a>
                <a href="/corporate/register/" class="primary-button" style="text-decoration: none; display: inline-block; margin: 0 10px;">Register</a>
            </div>
            {% else %}
            <p>You don't have permission to access this company's game.</p>
            {% endif %}
        </div>
        {% else %}

        <!-- Mobile Interface Structure -->
        <div class="mobile-interface" style="display: none;">
            <!-- Mobile Header -->
            <header class="mobile-header">
                <button class="mobile-header-btn" id="mobile-left-toggle" aria-label="Toggle game menu">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="mobile-header-title">Corporate Prompt Master</h1>
                {% if company %}
                <span class="mobile-company-badge">{{ company.name }}</span>
                {% endif %}
                <div class="mobile-header-spacer"></div>
            </header>

            <!-- Mobile Game Container -->
            <div class="mobile-game-container">
                <!-- Left Sidebar (Game Info) -->
                <div class="mobile-sidebar-left collapsed" id="mobile-sidebar-left">
                    <div class="mobile-sidebar-content">
                        <div class="mobile-card">
                            <h2><i class="fas fa-user-circle"></i> Current Contact</h2>
                            <div class="character-info">
                                <div id="mobile-character-avatar" class="avatar">👩‍💼</div>
                                <div id="mobile-character-name" class="character-name">Loading...</div>
                                <div id="mobile-character-title" class="character-title">Loading...</div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-chart-line"></i> Game Stats</h3>
                            <div class="mobile-stat-grid">
                                <div class="mobile-stat-item">
                                    <div class="mobile-form-label">Current Role</div>
                                    <div id="mobile-current-role" class="stat-value">Applicant</div>
                                </div>
                                <div class="mobile-stat-item">
                                    <div class="mobile-form-label">Score</div>
                                    <div id="mobile-performance-score" class="stat-value">0</div>
                                </div>
                                <div class="mobile-stat-item">
                                    <div class="mobile-form-label">Challenges</div>
                                    <div id="mobile-challenges-completed" class="stat-value">0</div>
                                </div>
                                <div class="mobile-stat-item">
                                    <div class="mobile-form-label">Progress</div>
                                    <div id="mobile-role-progress" class="stat-value">0/0</div>
                                </div>
                            </div>
                            <div class="mobile-progress-container">
                                <div id="mobile-progress-bar" class="mobile-progress-bar" style="width: 0%;"></div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-info-circle"></i> Context Info</h3>
                            <div class="mobile-context-item">
                                <span class="mobile-form-label">Manager:</span>
                                <span id="mobile-current-manager">HR</span>
                            </div>
                            <div class="mobile-context-item">
                                <span class="mobile-form-label">Task:</span>
                                <span id="mobile-current-task">Cover Letter</span>
                            </div>
                            {% if company %}
                            <div class="mobile-context-item">
                                <span class="mobile-form-label">Company:</span>
                                <span>{{ company.name }}</span>
                            </div>
                            {% if team %}
                            <div class="mobile-context-item">
                                <span class="mobile-form-label">Team:</span>
                                <span>{{ team }}</span>
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-gamepad"></i> How to Play</h3>
                            <ol class="mobile-instructions">
                                <li>Read the challenge from your contact</li>
                                <li>Craft a prompt for the AI assistant</li>
                                <li>Preview the AI's response</li>
                                <li>Edit the response if needed</li>
                                <li>Submit your final answer</li>
                            </ol>
                            <p class="mobile-tip">Complete challenges to advance your career!</p>
                        </div>

                        {% if not is_anonymous %}
                        <div class="mobile-card">
                            <h3><i class="fas fa-play-circle"></i> Game Controls</h3>
                            <div class="mobile-button-group">
                                <button id="mobile-continue-game" class="mobile-btn mobile-btn-primary">
                                    <i class="fas fa-play-circle"></i> Continue Game
                                </button>
                                <button id="mobile-restart-game" class="mobile-btn mobile-btn-secondary">
                                    <i class="fas fa-redo"></i> Restart Game
                                </button>
                            </div>

                            {% if game_session %}
                            <div class="mobile-game-progress">
                                <h4><i class="fas fa-trophy"></i> Your Progress</h4>
                                <div class="mobile-progress-item">
                                    <strong>Role:</strong> {{ game_session.current_role|title|cut:"_" }}
                                </div>
                                <div class="mobile-progress-item">
                                    <strong>Score:</strong> {{ game_session.performance_score }}
                                </div>
                                <div class="mobile-progress-item">
                                    <strong>Challenges:</strong> {{ game_session.challenges_completed }}
                                </div>
                                {% if game_session.game_completed %}
                                <div class="mobile-completion-badge">
                                    <i class="fas fa-check-circle"></i> Game Completed!
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="mobile-card">
                            <h3><i class="fas fa-user-plus"></i> Save Your Progress</h3>
                            <p>You're playing as a guest. To save your progress and appear on leaderboards, please log in or create an account.</p>
                            <div class="mobile-button-group">
                                <a href="/corporate/login/" class="mobile-btn mobile-btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Log In
                                </a>
                                <a href="/corporate/register/" class="mobile-btn mobile-btn-secondary">
                                    <i class="fas fa-user-plus"></i> Register
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <div class="mobile-card">
                            <h3><i class="fas fa-sitemap"></i> Company Hierarchy</h3>
                            <div id="mobile-org-chart-container" class="mobile-org-chart">
                                <div class="mobile-org-level">
                                    <div class="mobile-org-role executive">
                                        <i class="fas fa-crown"></i>
                                        <span class="role-title">EXECUTIVE</span>
                                        <span class="role-desc">C-Suite Leadership</span>
                                    </div>
                                </div>
                                <div class="mobile-org-level">
                                    <div class="mobile-org-role senior-manager">
                                        <i class="fas fa-user-tie"></i>
                                        <span class="role-title">SENIOR MANAGER</span>
                                        <span class="role-desc">Department Head</span>
                                    </div>
                                </div>
                                <div class="mobile-org-level">
                                    <div class="mobile-org-role manager">
                                        <i class="fas fa-users"></i>
                                        <span class="role-title">MANAGER</span>
                                        <span class="role-desc">Team Leader</span>
                                    </div>
                                </div>
                                <div class="mobile-org-level">
                                    <div class="mobile-org-role senior-employee">
                                        <i class="fas fa-user-graduate"></i>
                                        <span class="role-title">SENIOR EMPLOYEE</span>
                                        <span class="role-desc">Experienced Professional</span>
                                    </div>
                                </div>
                                <div class="mobile-org-level">
                                    <div class="mobile-org-role employee">
                                        <i class="fas fa-user"></i>
                                        <span class="role-title">EMPLOYEE</span>
                                        <span class="role-desc">Team Member</span>
                                    </div>
                                </div>
                                <div class="mobile-org-level current">
                                    <div class="mobile-org-role applicant current-role">
                                        <i class="fas fa-user-plus"></i>
                                        <span class="role-title">APPLICANT</span>
                                        <span class="role-desc">You are here</span>
                                        <div class="current-indicator">
                                            <i class="fas fa-arrow-left"></i> Current
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-route"></i> Career Path</h3>
                            <div id="mobile-role-progression-container" class="mobile-career-path">
                                <div class="mobile-career-step completed">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h4>Application</h4>
                                        <p>Submit your cover letter and resume</p>
                                        <div class="step-status">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-career-step current">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h4>Interview Process</h4>
                                        <p>Navigate through various interview rounds</p>
                                        <div class="step-status current">
                                            <i class="fas fa-play-circle"></i> In Progress
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-career-step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h4>Onboarding</h4>
                                        <p>Complete new employee orientation</p>
                                        <div class="step-status">
                                            <i class="fas fa-lock"></i> Locked
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-career-step">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <h4>First Projects</h4>
                                        <p>Handle initial work assignments</p>
                                        <div class="step-status">
                                            <i class="fas fa-lock"></i> Locked
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-career-step">
                                    <div class="step-number">5</div>
                                    <div class="step-content">
                                        <h4>Performance Review</h4>
                                        <p>Demonstrate your capabilities</p>
                                        <div class="step-status">
                                            <i class="fas fa-lock"></i> Locked
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-career-step">
                                    <div class="step-number">6</div>
                                    <div class="step-content">
                                        <h4>Promotion</h4>
                                        <p>Advance to the next level</p>
                                        <div class="step-status">
                                            <i class="fas fa-trophy"></i> Goal
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-star"></i> Role Requirements</h3>
                            <div class="mobile-requirements">
                                <div class="requirement-category">
                                    <h4><i class="fas fa-graduation-cap"></i> Skills Needed</h4>
                                    <ul class="requirement-list">
                                        <li>Professional Communication</li>
                                        <li>Problem Solving</li>
                                        <li>Team Collaboration</li>
                                        <li>Time Management</li>
                                        <li>Adaptability</li>
                                    </ul>
                                </div>

                                <div class="requirement-category">
                                    <h4><i class="fas fa-tasks"></i> Key Challenges</h4>
                                    <ul class="requirement-list">
                                        <li>Client Communication</li>
                                        <li>Project Management</li>
                                        <li>Stakeholder Relations</li>
                                        <li>Quality Assurance</li>
                                        <li>Innovation & Ideas</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        {% if company %}
                        <div class="mobile-card">
                            <h3><i class="fas fa-building"></i> {{ company.name }}</h3>
                            <div class="mobile-company-info">
                                {% if company.description %}
                                <p class="company-description">{{ company.description }}</p>
                                {% endif %}

                                <div class="company-stats">
                                    <div class="company-stat">
                                        <i class="fas fa-users"></i>
                                        <span class="stat-label">Employees</span>
                                        <span class="stat-value">{{ company.employee_count|default:"1000+" }}</span>
                                    </div>
                                    <div class="company-stat">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span class="stat-label">Location</span>
                                        <span class="stat-value">{{ company.location|default:"Global" }}</span>
                                    </div>
                                    <div class="company-stat">
                                        <i class="fas fa-industry"></i>
                                        <span class="stat-label">Industry</span>
                                        <span class="stat-value">{{ company.industry|default:"Technology" }}</span>
                                    </div>
                                </div>

                                {% if company.values %}
                                <div class="company-values">
                                    <h4><i class="fas fa-heart"></i> Company Values</h4>
                                    <div class="values-list">
                                        {% for value in company.values %}
                                        <span class="value-tag">{{ value }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <div class="mobile-card">
                            <h3><i class="fas fa-lightbulb"></i> Pro Tips</h3>
                            <div class="mobile-tips">
                                <div class="tip-item">
                                    <i class="fas fa-check-circle tip-icon"></i>
                                    <div class="tip-content">
                                        <strong>Be Specific:</strong> Clear, detailed prompts get better AI responses.
                                    </div>
                                </div>
                                <div class="tip-item">
                                    <i class="fas fa-edit tip-icon"></i>
                                    <div class="tip-content">
                                        <strong>Review & Edit:</strong> Always review AI responses before submitting.
                                    </div>
                                </div>
                                <div class="tip-item">
                                    <i class="fas fa-user-friends tip-icon"></i>
                                    <div class="tip-content">
                                        <strong>Stay Professional:</strong> Maintain appropriate tone for workplace.
                                    </div>
                                </div>
                                <div class="tip-item">
                                    <i class="fas fa-clock tip-icon"></i>
                                    <div class="tip-content">
                                        <strong>Take Your Time:</strong> Quality responses lead to better scores.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-question-circle"></i> Need Help?</h3>
                            <div class="mobile-help">
                                <p>Having trouble with the game? Here are some resources:</p>
                                <div class="mobile-button-group">
                                    <button class="mobile-btn mobile-btn-secondary" onclick="showMobileHelp()">
                                        <i class="fas fa-book"></i> Game Guide
                                    </button>
                                    <button class="mobile-btn mobile-btn-secondary" onclick="showMobileFeedback()">
                                        <i class="fas fa-comment"></i> Feedback
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-card">
                            <h3><i class="fas fa-keyboard"></i> Shortcuts</h3>
                            <div class="mobile-shortcuts">
                                <div class="mobile-shortcut-item">
                                    <span class="mobile-shortcut-key">Alt + L</span>
                                    <span class="mobile-shortcut-desc">Toggle this sidebar</span>
                                </div>
                                <div class="mobile-shortcut-item">
                                    <span class="mobile-shortcut-key">Escape</span>
                                    <span class="mobile-shortcut-desc">Close sidebar</span>
                                </div>
                                <div class="mobile-shortcut-item">
                                    <span class="mobile-shortcut-key">Swipe →</span>
                                    <span class="mobile-shortcut-desc">Open this sidebar</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="mobile-main-content">
                    <!-- Messages Container -->
                    <div class="mobile-messages-container" id="mobile-messages-container">
                        <!-- Messages will be populated by JavaScript -->
                    </div>

                    <!-- Mobile Preview Container -->
                    <div id="mobile-preview-container" class="mobile-preview-container hidden">
                        <div class="mobile-preview-header">
                            <h3 class="mobile-preview-title">
                                <i class="fas fa-eye"></i> AI Response Preview
                            </h3>
                            <div class="mobile-preview-actions">
                                <button id="mobile-close-preview" class="mobile-btn-icon" title="Close Preview">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mobile-preview-content">
                            <div id="mobile-preview-text" class="mobile-preview-text">
                                <!-- AI response preview will be displayed here -->
                            </div>
                            <div id="mobile-preview-feedback" class="mobile-preview-feedback hidden">
                                <!-- Feedback and scoring will be displayed here -->
                            </div>
                        </div>
                        <div class="mobile-preview-buttons">
                            <button id="mobile-edit-prompt-btn" class="mobile-btn mobile-btn-secondary">
                                <i class="fas fa-edit"></i> Edit Prompt
                            </button>
                            <button id="mobile-submit-preview-btn" class="mobile-btn mobile-btn-success">
                                <i class="fas fa-paper-plane"></i> Submit Response
                            </button>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <div class="mobile-input-area">
                        <div class="mobile-form-group">
                            <label class="mobile-form-label" for="mobile-prompt-input">Your Prompt</label>
                            <textarea
                                id="mobile-prompt-input"
                                class="mobile-input mobile-textarea"
                                placeholder="Enter your prompt here..."
                                rows="3"
                            ></textarea>
                            <div class="mobile-form-help">Craft a clear prompt for the AI assistant</div>
                        </div>
                        <div class="mobile-button-group">
                            <button id="mobile-preview-btn" class="mobile-btn mobile-btn-primary">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button id="mobile-submit-btn" class="mobile-btn mobile-btn-success" style="display: none;">
                                <i class="fas fa-paper-plane"></i> Submit
                            </button>
                            <button id="mobile-restart-btn" class="mobile-btn mobile-btn-secondary">
                                <i class="fas fa-redo"></i> Restart
                            </button>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- Desktop Interface (Hidden on Mobile) -->
        <div class="desktop-interface">
            <div class="app-container">
                <!-- Left Sidebar -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <div class="sidebar-collapse-indicator" title="Sidebar can be collapsed with Alt+S">
                            <span>◀</span>
                        </div>
                        <div class="logo">
                            <h1>Corporate Prompt Master</h1>
                            <div class="subtitle">Context Aware Edition</div>
                        </div>
                    </div>

                    <div class="character-info">
                        <div class="character-header">Current Contact:</div>
                        <div id="character-avatar" class="avatar">👩‍💼</div>
                        <div id="character-name" class="character-name">Loading...</div>
                        <div id="character-title" class="character-title">Loading...</div>
                    </div>

                    <div class="game-stats">
                        <div class="stat">
                            <div class="stat-label">Current Role:</div>
                            <div id="current-role" class="stat-value">Applicant</div>
                        </div>
                        <div class="stat">
                            <div class="stat-label">Performance Score:</div>
                            <div id="performance-score" class="stat-value">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-label">Challenges Completed:</div>
                            <div id="challenges-completed" class="stat-value">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-label">Role Progress:</div>
                            <div id="role-progress" class="stat-value">0/0</div>
                        </div>
                        <div class="progress-container">
                            <div id="progress-bar" class="progress-bar" style="width: 0%;"></div>
                        </div>

                        <!-- Context awareness indicators -->
                        <div class="context-info">
                            <div>
                                <span class="context-label">Current Manager:</span>
                                <span id="current-manager">HR</span>
                            </div>
                            <div>
                                <span class="context-label">Current Task:</span>
                                <span id="current-task">Cover Letter</span>
                            </div>
                            {% if company %}
                            <div class="company-info">
                                <span class="context-label">Company:</span>
                                <span>{{ company.name }}</span>
                            </div>
                            {% if team %}
                            <div class="team-info">
                                <span class="context-label">Team:</span>
                                <span>{{ team }}</span>
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>

                    <div class="instructions">
                        <h3>How to Play:</h3>
                        <ol>
                            <li>Read the challenge from your contact</li>
                            <li>Craft a prompt for the AI assistant</li>
                            <li>Preview the AI's response</li>
                            <li>Edit the response if needed</li>
                            <li>Submit your final answer</li>
                        </ol>
                        <p>Complete challenges to advance your career!</p>

                        {% if not is_anonymous %}
                        <div class="game-controls">
                            <button id="continue-game-button" class="primary-button" style="margin-top: 10px; width: 100%;">
                                <i class="fas fa-play-circle"></i> Continue Game
                            </button>
                            <button id="restart-game-button-sidebar" class="secondary-button" style="margin-top: 10px; width: 100%;">
                                <i class="fas fa-redo"></i> Restart Game
                            </button>

                            {% if game_session %}
                            <div class="game-progress" style="margin-top: 15px; padding: 10px; background-color: rgba(255,255,255,0.1); border-radius: 5px;">
                                <h4 style="margin-top: 0;">Your Progress</h4>
                                <div style="margin-bottom: 5px;">
                                    <strong>Role:</strong> {{ game_session.current_role|title|cut:"_" }}
                                </div>
                                <div style="margin-bottom: 5px;">
                                    <strong>Score:</strong> {{ game_session.performance_score }}
                                </div>
                                <div style="margin-bottom: 5px;">
                                    <strong>Challenges:</strong> {{ game_session.challenges_completed }}
                                </div>
                                {% if game_session.game_completed %}
                                <div style="color: #4CAF50; font-weight: bold; margin-top: 5px;">
                                    <i class="fas fa-check-circle"></i> Game Completed!
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Main Content -->
                <div class="main-content">
                    <div class="header">
                        <div class="mobile-sidebar-toggle" id="sidebar-toggle" title="Toggle Left Sidebar (Alt+S)">
                            <span></span>
                            <span></span>
                            <span></span>
                            <div class="shortcut-hint">Alt+S</div>
                        </div>
                        <div class="role-display">
                            Role: <span id="role-display">Applicant</span>
                        </div>
                        <div class="right-sidebar-toggle" id="right-sidebar-toggle" title="Toggle Right Sidebar (Alt+R)">
                            <span></span>
                            <span></span>
                            <span></span>
                            <div class="shortcut-hint">Alt+R</div>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div id="messages-container" class="messages-container">
                        <!-- Messages will be added here dynamically -->
                    </div>

                    <!-- Input Area -->
                    <div class="input-area">
                        <!-- Prompt Input -->
                        <div id="prompt-container" class="prompt-container">
                            <textarea id="prompt-input" class="prompt-input" placeholder="Enter your prompt here..."></textarea>
                            <div class="button-row d-flex gap-2 justify-content-between">
                                <button id="restart-game-button-main" class="btn btn-secondary">
                                    <i class="fas fa-redo-alt me-1"></i> Restart Game
                                </button>
                                <button id="preview-button" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i> Preview Response
                                </button>
                            </div>
                        </div>

                <!-- Preview Container -->
                <div id="preview-container" class="preview-container hidden">
                    <div class="preview-header">
                        <h3>AI Response Preview</h3>
                        <div class="preview-header-actions">
                            <button id="zoom-preview-button" class="icon-button" title="Zoom Preview">
                                <span class="zoom-icon">🔍</span>
                            </button>
                            <div class="quality-container">
                                <span id="quality-indicator" class="quality-indicator">GOOD</span>
                            </div>
                        </div>
                    </div>

                    <!-- Scrollable preview content -->
                    <div class="preview-scroll-container">
                        <!-- AI Response Preview -->
                        <div id="preview-content" class="preview-content"></div>

                        <!-- Feedback and Evaluation Section -->
                        <div id="similarity-container" class="similarity-container hidden">
                            <!-- Score and Basic Feedback -->
                            <div class="similarity-score-container">
                                <div class="similarity-label">Overall Score:</div>
                                <div id="similarity-score" class="similarity-score">0</div>
                            </div>
                            <div class="feedback-details-container">
                                <div class="feedback-details-label">Feedback:</div>
                                <ul id="feedback-details-list" class="feedback-details-list"></ul>
                            </div>

                            <!-- Prompt Evaluation Container -->
                            <div id="prompt-evaluation-container" class="prompt-evaluation-container hidden">
                                <div class="prompt-evaluation-header">
                                    <div class="prompt-evaluation-label">How to Improve Your Request:</div>
                                </div>
                                <div id="prompt-evaluation-summary" class="prompt-evaluation-summary"></div>
                                <div class="prompt-improvement-container">
                                    <div class="prompt-improvement-label">Try these tips:</div>
                                    <ul id="prompt-improvement-list" class="prompt-improvement-list"></ul>
                                </div>
                                <div id="prompt-dimensions-container" class="prompt-dimensions-container">
                                    <div class="prompt-dimensions-label">Detailed Feedback:</div>
                                    <div class="dimensions-grid" id="dimensions-grid"></div>
                                </div>
                            </div>

                            <!-- This section is intentionally removed to match the Flask app -->

                            <!-- This section is intentionally removed to match the Flask app -->

                            <!-- Manager Feedback Preview Container -->
                            <div id="manager-feedback-container" class="manager-feedback-container">
                                <div class="manager-feedback-header">
                                    <div class="manager-feedback-label">Manager Feedback Preview:</div>
                                </div>
                                <div id="manager-feedback-content" class="manager-feedback-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons (always visible) -->
                    <div class="preview-actions">
                        <button id="edit-prompt-button" class="secondary-button">Edit Prompt</button>
                        <button id="edit-response-button" class="secondary-button">Edit Response</button>
                        <button id="submit-button" class="primary-button">Submit Response</button>
                    </div>
                </div>

                <!-- Edit Response Container -->
                <div id="edit-response-container" class="edit-response-container hidden">
                    <div class="edit-header">
                        <h3>Edit AI Response</h3>
                    </div>
                    <textarea id="response-editor" class="response-editor"></textarea>
                    <div class="edit-actions">
                        <button id="back-to-preview-button" class="secondary-button">Back to Preview</button>
                        <button id="submit-edited-button" class="primary-button">Submit Edited Response</button>
                    </div>
                        </div>
                    </div>
                </div>

                <!-- Right Sidebar -->
                <div class="right-sidebar">
                    <div class="right-sidebar-header">
                        <div class="right-sidebar-collapse-indicator" title="Right sidebar can be collapsed with Alt+R">
                            <span>▶</span>
                        </div>
                        <h2>Career & Company</h2>
                    </div>

                    <!-- Company Hierarchy Section -->
                    <div id="org-chart-container" class="org-chart-container">
                        <h3>Company Hierarchy:</h3>
                        <div id="org-chart" class="org-chart">
                            <!-- Organization chart will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Career Path Section -->
                    <div id="role-progression-container" class="role-progression-container">
                        <div class="role-progression-header">
                            <h3>Career Path:</h3>
                            <button id="toggle-role-progression" class="toggle-button" title="Show/Hide Career Path">
                                <span class="toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div id="role-progression-content" class="role-progression-content">
                            <!-- Role progression visualization will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

        </div> <!-- End app-container -->
        </div> <!-- End desktop-interface -->
    {% endif %}

    <!-- Game Complete Modal -->
    <div id="game-complete-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Congratulations!</h2>
            <p>You've completed all challenges and reached the top of the corporate ladder!</p>
            <div class="final-score-container">
                <div class="final-score-label">Final Performance Score:</div>
                <div id="final-score" class="final-score">0</div>
            </div>
            <div class="modal-buttons">
                <button id="close-modal-button" class="secondary-button">Close</button>
                <button id="restart-game-button" class="primary-button">Play Again</button>
            </div>
        </div>
    </div>

    <!-- Restart Game Confirmation Modal -->
    <div id="restart-confirmation-modal" class="modal hidden">
        <div class="modal-content">
            <h2>{% trans "Restart Game?" %}</h2>
            <p>{% trans "Are you sure you want to restart the game? This will clear all your messages and reset your progress." %}</p>
            <div class="modal-buttons">
                <button id="cancel-restart-button" class="secondary-button">{% trans "Cancel" %}</button>
                <button id="confirm-restart-button" class="primary-button">{% trans "Yes, Restart Game" %}</button>
            </div>
        </div>
    </div>

    <!-- Login Prompt Modal for Anonymous Users -->
    <div id="login-prompt-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Save Your Progress</h2>
            <p>You're playing as a guest. To save your progress and appear on leaderboards, please log in or create an account.</p>
            <div class="modal-buttons">
                <button id="continue-as-guest-button" class="secondary-button">Continue as Guest</button>
                <a href="/corporate/login/" class="primary-button" style="text-decoration: none; display: inline-block;">Log In</a>
                <a href="/corporate/register/" class="primary-button" style="text-decoration: none; display: inline-block;">Register</a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
        <div class="loading-text">Processing...</div>
    </div>

    <!-- Offline Mode Alert -->
    <div id="offline-mode-alert" class="modal hidden">
        <div class="modal-content">
            <h2>Offline Mode Detected</h2>
            <p>The evaluation service is currently unavailable. Please check your internet connection and try again.</p>
            <p>The system cannot evaluate prompts while offline.</p>
            <div class="modal-buttons">
                <button id="close-offline-alert-button" class="primary-button">Close</button>
            </div>
        </div>
    </div>

    <!-- Theme Toggle Button Removed -->

    <!-- Zoom Preview Modal -->
    <div id="zoom-preview-modal" class="modal hidden">
        <div class="modal-content zoom-modal-content">
            <div class="zoom-modal-header">
                <h2>AI Response Preview</h2>
                <button id="close-zoom-modal-button" class="close-button">&times;</button>
            </div>
            <div id="zoomed-preview-content" class="zoomed-preview-content"></div>
        </div>
    </div>

    <!-- Task Failure Modal -->
    <div id="task-failure-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Task Not Completed</h2>
            <div id="failure-feedback" class="failure-feedback">
                Your submission did not meet the requirements for this task.
            </div>
            <div class="modal-buttons">
                <button id="retry-task-button" class="primary-button">Retry This Task</button>
            </div>
        </div>
    </div>

    <script src="{% static 'game/js/csrf.js' %}"></script>
    <script src="{% static 'game/js/api_endpoints.js' %}"></script>
    <script src="{% static 'game/js/context_aware_game.js' %}"></script>
    <script src="{% static 'game/js/org-chart.js' %}"></script>
    <script src="{% static 'game/js/task_definitions.js' %}"></script>
    <script src="{% static 'game/js/org-chart-integration.js' %}"></script>
    <script src="{% static 'game/js/celebration.js' %}"></script>
    <script src="{% static 'game/js/responsive-messages.js' %}"></script>
    <script src="{% static 'game/js/chart-hover-zoom.js' %}"></script>
    <script src="{% static 'game/js/role-progression-scroll.js' %}"></script>
    <script src="{% static 'game/js/hide-right-toggle.js' %}"></script>
    <!-- Professional Mobile Interface JavaScript -->
    <script src="{% static 'game/js/mobile-professional.js' %}"></script>
    <script>
        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired');

            // Add error logging
            window.onerror = function(message, source, lineno, colno, error) {
                // Log to console
                console.error('Error:', message, 'at', source, lineno, colno);

                // Send error to server
                fetch('/game/api/log_error/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        message: message,
                        source: source,
                        lineno: lineno,
                        colno: colno,
                        error: error ? error.stack : null
                    })
                }).catch(err => console.error('Failed to log error:', err));

                return false;
            };

            // Check if API_ENDPOINTS is defined
            console.log('API_ENDPOINTS defined:', typeof API_ENDPOINTS !== 'undefined');
            if (typeof API_ENDPOINTS !== 'undefined') {
                console.log('API_ENDPOINTS:', API_ENDPOINTS);
            }

            // Check if initGame is defined
            console.log('initGame defined:', typeof initGame !== 'undefined');

            // Initialize the game
            if (typeof initGame === 'function') {
                console.log('Calling initGame function');
                setTimeout(function() {
                    initGame();
                }, 1000); // Add a delay to ensure all scripts are loaded
            } else {
                console.error('initGame function not found');
            }
        });
    </script>

    <style>
        /* Additional dark mode fixes for code elements */
        html.dark-mode pre, body.dark-mode pre,
        html.dark-mode code, body.dark-mode code {
            background-color: #2a2a2a !important;
            color: #e0e0e0 !important;
        }

        html.dark-mode pre code, body.dark-mode pre code {
            background-color: transparent !important;
        }

        /* Fix for syntax highlighting in dark mode */
        html.dark-mode .hljs, body.dark-mode .hljs {
            background-color: #2a2a2a !important;
            color: #e0e0e0 !important;
        }

        /* Fix for developer tools panel in dark mode */
        html.dark-mode .CodeMirror,
        body.dark-mode .CodeMirror,
        html.dark-mode .cm-s-default,
        body.dark-mode .cm-s-default {
            background-color: #2a2a2a !important;
            color: #e0e0e0 !important;
        }

        html.dark-mode .CodeMirror-gutters,
        body.dark-mode .CodeMirror-gutters {
            background-color: #252525 !important;
            border-right: 1px solid #333 !important;
        }

        html.dark-mode .CodeMirror-linenumber,
        body.dark-mode .CodeMirror-linenumber {
            color: #777 !important;
        }

        html.dark-mode .CodeMirror-cursor,
        body.dark-mode .CodeMirror-cursor {
            border-left: 1px solid #e0e0e0 !important;
        }

        html.dark-mode .CodeMirror-selected,
        body.dark-mode .CodeMirror-selected {
            background-color: rgba(74, 134, 232, 0.3) !important;
        }

        html.dark-mode .CodeMirror-activeline-background,
        body.dark-mode .CodeMirror-activeline-background {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        html.dark-mode .cm-s-default .cm-keyword,
        body.dark-mode .cm-s-default .cm-keyword {
            color: #88c !important;
        }

        html.dark-mode .cm-s-default .cm-string,
        body.dark-mode .cm-s-default .cm-string {
            color: #a5d6a7 !important;
        }

        html.dark-mode .cm-s-default .cm-number,
        body.dark-mode .cm-s-default .cm-number {
            color: #f78c6c !important;
        }

        html.dark-mode .cm-s-default .cm-comment,
        body.dark-mode .cm-s-default .cm-comment {
            color: #999 !important;
        }

        /* Fix for any other code-related elements */
        html.dark-mode [class*="code-"],
        body.dark-mode [class*="code-"],
        html.dark-mode [class*="syntax-"],
        body.dark-mode [class*="syntax-"],
        html.dark-mode [class*="editor-"],
        body.dark-mode [class*="editor-"] {
            background-color: #2a2a2a !important;
            color: #e0e0e0 !important;
        }

        /* Fix for browser developer tools elements */
        html.dark-mode .devtools-syntax-highlighting,
        body.dark-mode .devtools-syntax-highlighting,
        html.dark-mode .webkit-html-attribute-name,
        body.dark-mode .webkit-html-attribute-name,
        html.dark-mode .webkit-html-attribute-value,
        body.dark-mode .webkit-html-attribute-value,
        html.dark-mode .webkit-html-comment,
        body.dark-mode .webkit-html-comment,
        html.dark-mode .webkit-html-tag,
        body.dark-mode .webkit-html-tag,
        html.dark-mode .webkit-html-text-node,
        body.dark-mode .webkit-html-text-node,
        html.dark-mode .webkit-css-property,
        body.dark-mode .webkit-css-property,
        html.dark-mode .webkit-css-value,
        body.dark-mode .webkit-css-value,
        html.dark-mode .webkit-javascript-keyword,
        body.dark-mode .webkit-javascript-keyword,
        html.dark-mode .webkit-javascript-comment,
        body.dark-mode .webkit-javascript-comment,
        html.dark-mode .webkit-javascript-string,
        body.dark-mode .webkit-javascript-string,
        html.dark-mode .webkit-javascript-number,
        body.dark-mode .webkit-javascript-number,
        html.dark-mode .webkit-javascript-ident,
        body.dark-mode .webkit-javascript-ident {
            color: #e0e0e0 !important;
        }

        /* Additional fixes for browser developer tools */
        html.dark-mode .styles-section,
        body.dark-mode .styles-section,
        html.dark-mode .styles-sidebar-pane-toolbar,
        body.dark-mode .styles-sidebar-pane-toolbar,
        html.dark-mode .styles-selector,
        body.dark-mode .styles-selector,
        html.dark-mode .sidebar-pane,
        body.dark-mode .sidebar-pane,
        html.dark-mode .pane,
        body.dark-mode .pane,
        html.dark-mode .monospace,
        body.dark-mode .monospace,
        html.dark-mode .source-code,
        body.dark-mode .source-code {
            background-color: #2a2a2a !important;
            color: #e0e0e0 !important;
        }

        /* Force all elements with style attribute in dark mode */
        html.dark-mode [style*="background-color: white"],
        body.dark-mode [style*="background-color: white"],
        html.dark-mode [style*="background-color: #fff"],
        body.dark-mode [style*="background-color: #fff"],
        html.dark-mode [style*="background-color: rgb(255, 255, 255)"],
        body.dark-mode [style*="background-color: rgb(255, 255, 255)"] {
            background-color: #2a2a2a !important;
        }

        html.dark-mode [style*="color: black"],
        body.dark-mode [style*="color: black"],
        html.dark-mode [style*="color: #000"],
        body.dark-mode [style*="color: #000"],
        html.dark-mode [style*="color: rgb(0, 0, 0)"],
        body.dark-mode [style*="color: rgb(0, 0, 0)"] {
            color: #e0e0e0 !important;
        }

        /* Dark mode for context info */
        html.dark-mode .context-info,
        body.dark-mode .context-info {
            background-color: #2a2a2a;
            color: #e0e0e0;
        }

        html.dark-mode .context-label,
        body.dark-mode .context-label {
            color: #adb5bd;
        }

        html.dark-mode .company-info,
        body.dark-mode .company-info,
        html.dark-mode .team-info,
        body.dark-mode .team-info {
            border-top-color: rgba(255, 255, 255, 0.1);
        }
    </style>
    <script>
        // Handle responsive layout for sidebars and content
        function handleResponsiveLayout() {
            const mobileRightSidebarContent = document.querySelector('.mobile-right-sidebar-content');
            if (!mobileRightSidebarContent) return;

            // Get the right sidebar toggle button
            const rightToggle = document.getElementById('right-sidebar-toggle');

            // Mobile view (< 768px)
            if (window.innerWidth <= 768) {
                // Show mobile version of right sidebar content in left sidebar
                mobileRightSidebarContent.classList.remove('hidden');

                // Show left sidebar by default on mobile
                document.body.classList.remove('sidebar-hidden');
                // Right sidebar is completely hidden via CSS

                // Hide right sidebar toggle button
                if (rightToggle) {
                    rightToggle.style.display = 'none';
                    rightToggle.style.visibility = 'hidden';
                }
            }
            // Tablet view (768px - 992px)
            else if (window.innerWidth <= 992) {
                // Hide mobile version of right sidebar content
                mobileRightSidebarContent.classList.add('hidden');

                // Show left sidebar by default on tablet
                document.body.classList.remove('sidebar-hidden');
                // Right sidebar is completely hidden via CSS

                // Hide right sidebar toggle button
                if (rightToggle) {
                    rightToggle.style.display = 'none';
                    rightToggle.style.visibility = 'hidden';
                }
            }
            // Desktop view (> 992px)
            else {
                // Hide mobile version of right sidebar content
                mobileRightSidebarContent.classList.add('hidden');

                // Show both sidebars by default on desktop
                document.body.classList.remove('sidebar-hidden');
                document.body.classList.remove('right-sidebar-hidden');

                // Show right sidebar toggle button
                if (rightToggle) {
                    rightToggle.style.display = '';
                    rightToggle.style.visibility = '';
                }
            }
        }

        // Call on load and resize
        window.addEventListener('resize', handleResponsiveLayout);
        window.addEventListener('load', handleResponsiveLayout);

        // Show debug button if URL has debug parameter
        if (window.location.search.includes('debug=true')) {
            const debugButton = document.getElementById('debug-button');
            if (debugButton) {
                debugButton.style.display = 'block';
            }
        }

        // Add a direct update function for the org chart that can be called from the console
        window.updateOrgChartWithRole = function(role) {
            console.log('Manual update of org chart with role:', role);
            if (window.forceUpdateOrgChart) {
                window.forceUpdateOrgChart(role);
            } else if (window.updateOrgChart) {
                // Update game state first
                if (window.gameState) {
                    window.gameState.currentRole = role;
                    if (role === 'junior_assistant' && (!window.gameState.completedRoles || !window.gameState.completedRoles.includes('applicant'))) {
                        if (!window.gameState.completedRoles) {
                            window.gameState.completedRoles = [];
                        }
                        window.gameState.completedRoles.push('applicant');
                    }
                }
                window.updateOrgChart();
            }
        };

        // Ensure role progression and org chart content is initialized
        window.addEventListener('load', function() {
            // Initialize responsive layout
            handleResponsiveLayout();
            console.log('Window loaded, checking visualizations');

            // Check role progression content
            const roleProgressionContent = document.getElementById('role-progression-content');
            const mobileRoleProgressionContent = document.getElementById('mobile-role-progression-content');
            if (roleProgressionContent) {
                if (roleProgressionContent.innerHTML.trim() === '') {
                    console.log('Role progression content is empty, setting placeholder');
                    roleProgressionContent.innerHTML = '<div class="role-progression-placeholder">Career progression visualization loading...</div>';

                    // Also set for mobile if it exists
                    if (mobileRoleProgressionContent) {
                        mobileRoleProgressionContent.innerHTML = '<div class="role-progression-placeholder">Career progression visualization loading...</div>';
                    }
                }
            }

            // Check org chart content
            const orgChart = document.getElementById('org-chart');
            const mobileOrgChart = document.getElementById('mobile-org-chart');
            if (orgChart) {
                if (orgChart.innerHTML.trim() === '') {
                    console.log('Org chart content is empty, setting placeholder');
                    orgChart.innerHTML = '<div class="org-chart-placeholder">Organization chart loading...</div>';

                    // Also set for mobile if it exists
                    if (mobileOrgChart) {
                        mobileOrgChart.innerHTML = '<div class="org-chart-placeholder">Organization chart loading...</div>';
                    }
                }
            }

            // Try to fetch game state for both visualizations
            fetch(API_ENDPOINTS.GET_GAME_STATE)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        console.log('Successfully fetched game state:', data.current_role);

                        // Update game state with current role and completed roles
                        if (window.gameState) {
                            window.gameState.currentRole = data.current_role || window.gameState.currentRole;
                            window.gameState.completedRoles = data.completed_roles || window.gameState.completedRoles || [];
                            console.log('Updated gameState with current role:', window.gameState.currentRole);
                        }

                        // Update role progression
                        if (data.role_progression_html) {
                            // Update main role progression
                            if (roleProgressionContent) {
                                console.log('Setting role progression HTML from window.onload fetch');
                                roleProgressionContent.innerHTML = data.role_progression_html;
                            }

                            // Update mobile role progression
                            if (mobileRoleProgressionContent) {
                                console.log('Setting mobile role progression HTML from window.onload fetch');
                                mobileRoleProgressionContent.innerHTML = data.role_progression_html;
                            }
                        }

                        // Update org chart
                        if (data.org_chart_html) {
                            // Remove any HTML comments that might contain "system undefined"
                            let cleanHtml = data.org_chart_html.replace(/<!--[\s\S]*?-->/g, '');

                            // Update main org chart
                            const orgChartElement = document.getElementById('org-chart');
                            if (orgChartElement) {
                                console.log('Setting org chart HTML from window.onload fetch');
                                orgChartElement.innerHTML = cleanHtml;
                            }

                            // Update mobile org chart
                            const mobileOrgChart = document.getElementById('mobile-org-chart');
                            if (mobileOrgChart) {
                                console.log('Setting mobile org chart HTML from window.onload fetch');
                                mobileOrgChart.innerHTML = cleanHtml;
                            }
                        } else if (window.gameState && window.generateOrgChartHtml) {
                            // Generate org chart HTML manually if not provided by server
                            console.log('Generating org chart HTML manually');
                            const html = window.generateOrgChartHtml(window.gameState.currentRole, window.gameState.completedRoles || []);
                            if (html) {
                                const orgChartElement = document.getElementById('org-chart');
                                const mobileOrgChartElement = document.getElementById('mobile-org-chart');
                                if (orgChartElement) orgChartElement.innerHTML = html;
                                if (mobileOrgChartElement) mobileOrgChartElement.innerHTML = html;
                            }
                        }

                        // Force update org chart after a short delay to ensure all scripts are loaded
                        setTimeout(function() {
                            if (window.updateOrgChart) {
                                console.log('Force updating org chart after delay');
                                window.updateOrgChart();
                            }

                            // Initialize hover zoom functionality
                            if (window.initChartHoverZoom) {
                                console.log('Initializing chart hover zoom');
                                window.initChartHoverZoom();
                            }
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Error fetching game state in window.onload:', error);
                });
        });

        // Additional script to ensure right sidebar toggle is hidden in mobile/tablet view
        window.addEventListener('load', function() {
            // Hide right sidebar toggle in mobile and tablet views
            function hideRightToggle() {
                if (window.innerWidth <= 1024) {
                    const rightToggle = document.getElementById('right-sidebar-toggle');
                    if (rightToggle) {
                        rightToggle.style.display = 'none';
                        rightToggle.style.visibility = 'hidden';
                        rightToggle.style.opacity = '0';
                        rightToggle.style.pointerEvents = 'none';
                    }
                }
            }

            // Execute immediately
            hideRightToggle();

            // Also on resize
            window.addEventListener('resize', hideRightToggle);

            // Also on orientation change
            window.addEventListener('orientationchange', hideRightToggle);
        });

        // Game Header functionality
        document.addEventListener('DOMContentLoaded', function() {
            const gameHeader = document.getElementById('game-header');
            const hoverHeader = document.getElementById('hover-header');

            // Hide the hover header if it exists (for backward compatibility)
            if (hoverHeader) {
                hoverHeader.style.display = 'none';
            }

            // No need for show/hide functionality as the header is now fixed
        });

        // Always use dark mode
        document.addEventListener('DOMContentLoaded', function() {
            // Apply dark mode to both html and body elements
            function applyDarkMode() {
                document.documentElement.classList.add('dark-mode');
                document.body.classList.add('dark-mode');
            }

            // Always apply dark mode on page load
            applyDarkMode();
            console.log('Applied dark mode on page load');

            // Store dark mode preference in localStorage
            localStorage.setItem('theme', 'dark');
        });
    </script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Header is now fixed by default, no initialization needed -->
</body>
</html>
