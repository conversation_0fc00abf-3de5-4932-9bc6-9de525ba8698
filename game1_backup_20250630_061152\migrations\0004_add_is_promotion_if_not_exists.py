# Generated by Django 5.0.6 on 2025-05-11 07:45

from django.db import migrations, models

def add_is_promotion_column(apps, schema_editor):
    # Get the database connection
    connection = schema_editor.connection
    cursor = connection.cursor()

    # Check if the column already exists using PostgreSQL-compatible query
    try:
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'game_message' AND column_name = 'is_promotion'
        """)
        column_exists = cursor.fetchone() is not None

        # If is_promotion column doesn't exist, add it
        if not column_exists:
            cursor.execute("ALTER TABLE game_message ADD COLUMN is_promotion boolean DEFAULT false")

    except Exception as e:
        # If table doesn't exist yet, that's fine - the column will be created with the table
        pass

    # Close the cursor
    cursor.close()

class Migration(migrations.Migration):

    dependencies = [
        ('game', '0003_message_is_promotion'),
    ]

    operations = [
        migrations.RunPython(add_is_promotion_column),
    ]
