/* Company Hierarchy Styles */

.company-hierarchy {
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.company-hierarchy h3 {
    margin-bottom: 10px;
    color: #333;
    text-align: center;
}

.hierarchy-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
    max-height: none; /* Removed fixed height */
    overflow-y: visible; /* Changed from auto to visible */
    padding-right: 0; /* Removed padding for scrollbar */
}

.hierarchy-level {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.hierarchy-level-title {
    font-weight: bold;
    color: #666;
    font-size: 0.8em;
    text-align: center;
    margin-bottom: 2px;
}

.hierarchy-roles {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.hierarchy-role {
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
    position: relative;
    transition: all 0.2s ease;
}

/* Current role styling */
.hierarchy-role.current {
    background-color: #e3f2fd;
    border-color: #90caf9;
    color: #0d47a1;
    font-weight: bold;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.4);
    transform: scale(1.05);
    z-index: 5;
}

.hierarchy-role.current::before {
    content: '➤';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: #2196f3;
    font-weight: bold;
}

/* Completed role styling */
.hierarchy-role.completed {
    background-color: #e8f5e9;
    border-color: #a5d6a7;
    color: #1b5e20;
}

.hierarchy-role.completed::after {
    content: '✓';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4caf50;
}

/* Future role styling */
.hierarchy-role.future {
    background-color: #f5f5f5;
    border-color: #e0e0e0;
    color: #9e9e9e;
}

/* Responsive styles */
@media (max-width: 768px) {
    .hierarchy-role {
        padding: 6px 10px;
        font-size: 0.85em;
    }
}
