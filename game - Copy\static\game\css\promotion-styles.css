/* Promotion message styles */
.message.from-them.is-promotion {
    background-color: rgba(255, 215, 0, 0.05);
    border-left: 6px solid #ffc107;
    padding: 20px;
    margin: 25px 0;
    animation: promotion-glow 2.5s infinite alternate;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.message.from-them.is-promotion::before {
    content: "";
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.message.from-them.is-promotion::after {
    content: "";
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

/* Dark mode styles for promotion messages */
.dark-mode .message.from-them.is-promotion {
    background-color: rgba(255, 215, 0, 0.08);
    border-left: 6px solid #ffc107;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.dark-mode .message.from-them.is-promotion::before,
.dark-mode .message.from-them.is-promotion::after {
    opacity: 0.7;
}

/* Promotion message header */
.message.is-promotion h2 {
    color: #f57c00;
    font-size: 2em;
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.03em;
    position: relative;
    display: inline-block;
    width: 100%;
    padding: 10px 0;
    background: linear-gradient(90deg, rgba(255,193,7,0) 0%, rgba(255,193,7,0.2) 50%, rgba(255,193,7,0) 100%);
    border-radius: 4px;
    animation: congratsGlow 3s infinite alternate;
}

.message.is-promotion h2:first-of-type {
    margin-bottom: 5px;
}

.dark-mode .message.is-promotion h2 {
    color: #ffb74d;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    background: linear-gradient(90deg, rgba(255,193,7,0) 0%, rgba(255,193,7,0.15) 50%, rgba(255,193,7,0) 100%);
}

@keyframes congratsGlow {
    from {
        text-shadow: 0 0 5px rgba(255, 152, 0, 0.3);
    }
    to {
        text-shadow: 0 0 15px rgba(255, 152, 0, 0.6), 0 0 5px rgba(255, 152, 0, 0.3);
    }
}

/* Special styling for CEO promotion messages */
.message.is-promotion .message-sender-ceo {
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.2) 0%, transparent 100%);
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    color: #e65100;
    font-size: 1.15rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.message.is-promotion .message-sender-ceo::before {
    content: "👑";
    margin-right: 8px;
    font-size: 1em;
}

.message.is-promotion .message-sender-ceo::after {
    background: linear-gradient(90deg, #ff9800 0%, transparent 100%);
    height: 3px;
}

.dark-mode .message.is-promotion .message-sender-ceo {
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.3) 0%, transparent 100%);
    color: #ffe0b2; /* Much lighter orange for better contrast */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.dark-mode .message.is-promotion .message-sender-ceo::after {
    background: linear-gradient(90deg, #ffb300 0%, transparent 100%);
}

/* Manager info - keeping for backward compatibility */
.manager-info {
    margin: 25px 0;
    padding: 15px;
    background-color: #e8f5e9;
    border-radius: 10px;
    border-left: 5px solid #43a047;
    font-size: 1.15em;
    text-align: center;
    color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
}

.dark-mode .manager-info {
    background-color: #1e3b1e;
    color: #e0e0e0;
    border-left: 5px solid #4caf50;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.manager-info strong {
    color: #2e7d32;
    font-weight: bold;
    font-size: 1.2em;
}

.dark-mode .manager-info strong {
    color: #81c784;
}

/* Promotion note */
.promotion-note {
    font-style: italic;
    margin: 20px 0;
    padding: 10px 15px;
    color: #555;
    text-align: center;
    background-color: #fff8e1;
    border-radius: 8px;
    border-left: 3px solid #ffc107;
    display: inline-block;
    font-size: 1.05em;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.dark-mode .promotion-note {
    color: #e0e0e0;
    background-color: #3e2f00;
    border-left: 3px solid #ffd54f;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Improvement section */
.improvement-section {
    margin: 25px 0;
    padding: 20px;
    background-color: #f3e5f5;
    border-radius: 10px;
    border-left: 5px solid #9c27b0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.improvement-section::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(156, 39, 176, 0.1) 0%, transparent 70%);
    z-index: 0;
}

.dark-mode .improvement-section {
    background-color: #2a1b2d;
    border-left: 5px solid #ba68c8;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-mode .improvement-section::before {
    background: radial-gradient(circle, rgba(186, 104, 200, 0.15) 0%, transparent 70%);
}

.improvement-section h3 {
    color: #6a1b9a;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.3em;
    letter-spacing: 0.02em;
    position: relative;
    z-index: 1;
    display: inline-block;
    padding-bottom: 5px;
    border-bottom: 2px solid rgba(156, 39, 176, 0.3);
}

.dark-mode .improvement-section h3 {
    color: #ce93d8;
    border-bottom: 2px solid rgba(186, 104, 200, 0.3);
}

/* Tips section styling */
.tips-section {
    margin-top: 15px;
}

.tips-section h4 {
    color: #7b1fa2;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.dark-mode .tips-section h4 {
    color: #ab47bc;
}

.tips-section ul {
    padding-left: 20px;
    margin-bottom: 0;
}

.tips-section li {
    margin-bottom: 8px;
    color: #555;
    line-height: 1.5;
    position: relative;
}

.dark-mode .tips-section li {
    color: #bbb;
}

.tips-section li::before {
    content: "✓";
    color: #7b1fa2;
    position: absolute;
    left: -20px;
    font-weight: bold;
}

.dark-mode .tips-section li::before {
    color: #ab47bc;
}

/* Animation for promotion message */
@keyframes promotion-glow {
    from {
        box-shadow: 0 0 10px rgba(255, 193, 7, 0.2);
    }
    to {
        box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
    }
}

.improvement-section h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.dark-mode .improvement-section h3 {
    color: #ddd;
}

/* Tips section */
.tips-section {
    margin-top: 10px;
}

.tips-section h4 {
    color: #555;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1em;
}

.dark-mode .tips-section h4 {
    color: #bbb;
}

.tips-section ul {
    margin-top: 5px;
    margin-left: 20px;
    padding-left: 0;
}

.tips-section li {
    margin-bottom: 5px;
    color: #444;
}

.dark-mode .tips-section li {
    color: #ccc;
}

/* Glow animation for promotion messages */
@keyframes promotion-glow {
    from {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.2);
    }
    to {
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    }
}

/* Dark mode glow animation */
.dark-mode .message.from-them.is-promotion {
    animation: promotion-glow-dark 2s infinite alternate;
}

@keyframes promotion-glow-dark {
    from {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
    }
}
