// Preview JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const prompt = urlParams.get('prompt');
    const taskId = urlParams.get('task_id');
    
    // Elements
    const promptText = document.getElementById('prompt-text');
    const evaluationSummary = document.getElementById('evaluation-summary');
    const improvementTips = document.getElementById('improvement-tips');
    const dimensionsGrid = document.getElementById('dimensions-grid');
    const responseText = document.getElementById('response-text');
    const editPromptBtn = document.getElementById('edit-prompt-btn');
    const submitPromptBtn = document.getElementById('submit-prompt-btn');
    
    // Display the prompt
    if (prompt) {
        promptText.textContent = prompt;
    } else {
        promptText.textContent = 'No prompt provided.';
    }
    
    // Fetch preview data
    if (prompt && taskId) {
        fetchPreview(prompt, taskId);
    }
    
    // Button event listeners
    editPromptBtn.addEventListener('click', function() {
        window.history.back();
    });
    
    submitPromptBtn.addEventListener('click', function() {
        submitPrompt(prompt, taskId);
    });
    
    // Function to fetch preview data
    async function fetchPreview(prompt, taskId) {
        try {
            const response = await fetch(`/preview_response?prompt=${encodeURIComponent(prompt)}&task_id=${encodeURIComponent(taskId)}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                // Display AI response
                responseText.innerHTML = data.ai_response_html;
                
                // Display evaluation summary
                if (data.prompt_evaluation_summary) {
                    evaluationSummary.textContent = data.prompt_evaluation_summary;
                }
                
                // Display improvement tips
                if (data.prompt_improvement_suggestions && data.prompt_improvement_suggestions.length > 0) {
                    improvementTips.innerHTML = '';
                    data.prompt_improvement_suggestions.forEach(tip => {
                        const li = document.createElement('li');
                        li.textContent = tip;
                        improvementTips.appendChild(li);
                    });
                }
                
                // Display dimensions grid
                if (data.prompt_dimensions && Object.keys(data.prompt_dimensions).length > 0) {
                    dimensionsGrid.innerHTML = '';
                    
                    // Create more user-friendly dimension names
                    const dimensionNames = {
                        'clarity': 'Clear Instructions',
                        'context': 'Background Info',
                        'completeness': 'Complete Details',
                        'task_alignment': 'Purpose',
                        'output_constraints': 'Format Preferences',
                        'model_awareness': 'Reasonable Request'
                    };
                    
                    // Add each dimension as a card
                    Object.entries(data.prompt_dimensions).forEach(([name, info]) => {
                        const score = info.score || 0;
                        const feedback = info.feedback || '';
                        const suggestions = info.suggestions || [];
                        
                        // Create dimension card
                        const card = document.createElement('div');
                        card.className = 'dimension-card';
                        
                        // Determine score class
                        let scoreClass = 'medium';
                        if (score >= 75) scoreClass = 'high';
                        else if (score < 50) scoreClass = 'low';
                        
                        // Create card HTML
                        card.innerHTML = `
                            <div class="dimension-header">
                                <div class="dimension-name">${dimensionNames[name] || name}</div>
                                <div class="dimension-score ${scoreClass}">${score}/100</div>
                            </div>
                            <div class="dimension-feedback">${feedback}</div>
                            ${suggestions.length > 0 ? `<div class="dimension-suggestions">${suggestions[0]}</div>` : ''}
                        `;
                        
                        dimensionsGrid.appendChild(card);
                    });
                }
            } else {
                console.error('Preview failed:', data.message);
                alert('Failed to generate preview. Please try again.');
            }
        } catch (error) {
            console.error('Error fetching preview:', error);
            alert('Error connecting to the server. Please check your connection and try again.');
        }
    }
    
    // Function to submit prompt
    async function submitPrompt(prompt, taskId) {
        try {
            const response = await fetch('/submit_prompt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: prompt,
                    task_id: taskId
                })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                // Redirect to game page
                window.location.href = '/';
            } else {
                console.error('Submission failed:', data.message);
                alert('Failed to submit prompt. Please try again.');
            }
        } catch (error) {
            console.error('Error submitting prompt:', error);
            alert('Error connecting to the server. Please check your connection and try again.');
        }
    }
});
