import importlib.util
import sys

def check_module(module_name):
    """Check if a module is installed."""
    spec = importlib.util.find_spec(module_name)
    if spec is None:
        print(f"❌ {module_name} is NOT installed")
        return False
    else:
        print(f"✅ {module_name} is installed")
        return True

# Required modules
required_modules = [
    "flask",
    "flask_cors",
    "markdown",
    "dotenv",
    "openai",
    "datetime",
    "uuid",
    "logging",
    "json",
    "re"
]

print("Checking required dependencies for Context-Aware Game...\n")

# Check each module
all_installed = True
for module in required_modules:
    if not check_module(module):
        all_installed = False

# Print summary
print("\nSummary:")
if all_installed:
    print("✅ All required dependencies are installed. You can run the game!")
else:
    print("❌ Some dependencies are missing. Please install them using pip:")
    print("\npip install flask flask-cors python-dotenv markdown openai\n")

input("Press Enter to exit...")
