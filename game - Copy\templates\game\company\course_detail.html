{% extends 'game/company/base.html' %}

{% block title %}{{ course.name }} - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2">{{ course.name }}</h1>
            <p class="text-muted mb-0">
                {% if course.is_active %}
                <span class="badge bg-success">Active</span>
                {% else %}
                <span class="badge bg-secondary">Inactive</span>
                {% endif %}
                
                {% if course.is_public %}
                <span class="badge bg-info">Public</span>
                {% else %}
                <span class="badge bg-warning text-dark">Restricted</span>
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'game:company_courses' company_slug=company.slug %}" class="btn btn-outline-primary me-2">
                <i class="bi bi-arrow-left"></i> Back to Courses
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editCourseModal">
                <i class="bi bi-pencil"></i> Edit Course
            </button>
        </div>
    </div>
    
    <!-- Course Info -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Course Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6>Description</h6>
                        <p>{{ course.description|default:"No description provided." }}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Starting Role</h6>
                                <p>{{ course.start_role|title }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Maximum Role</h6>
                                <p>{{ course.max_role|title|default:"No limit" }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Teams</h6>
                        {% if course.teams %}
                        <div class="d-flex flex-wrap gap-1">
                            {% for team in course.get_teams_list %}
                            <span class="badge bg-secondary">{{ team }}</span>
                            {% endfor %}
                        </div>
                        {% else %}
                        <p class="text-muted">No specific teams assigned.</p>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Created</h6>
                                <p>{{ course.created_at|date:"F j, Y" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6>Last Updated</h6>
                                <p>{{ course.updated_at|date:"F j, Y" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Course Stats</h5>
                </div>
                <div class="card-body">
                    <div class="stats-card text-center mb-3">
                        <div class="icon">
                            <i class="bi bi-list-check"></i>
                        </div>
                        <div class="value">{{ tasks_by_role|length }}</div>
                        <div class="label">Total Tasks</div>
                    </div>
                    
                    <div class="stats-card text-center mb-3">
                        <div class="icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="value">{{ course.completions.count }}</div>
                        <div class="label">Participants</div>
                    </div>
                    
                    <div class="stats-card text-center">
                        <div class="icon">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <div class="value">{{ course.completions.filter.is_completed.count }}</div>
                        <div class="label">Completions</div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="#" class="btn btn-success w-100">
                        <i class="bi bi-play-fill"></i> Play Course
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Course Tasks -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Course Tasks</h5>
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                <i class="bi bi-plus-circle"></i> Add Task
            </button>
        </div>
        <div class="card-body">
            {% if tasks_by_role %}
            <div class="accordion" id="tasksAccordion">
                {% for role, tasks in tasks_by_role.items %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ role|slugify }}">
                        <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ role|slugify }}" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="collapse{{ role|slugify }}">
                            <strong>{{ role|title }}</strong> <span class="badge bg-secondary ms-2">{{ tasks|length }} tasks</span>
                        </button>
                    </h2>
                    <div id="collapse{{ role|slugify }}" class="accordion-collapse collapse {% if forloop.first %}show{% endif %}" aria-labelledby="heading{{ role|slugify }}" data-bs-parent="#tasksAccordion">
                        <div class="accordion-body p-0">
                            <div class="list-group list-group-flush">
                                {% for task in tasks %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ task.title }}</h6>
                                            <p class="mb-1 small text-muted">{{ task.description|truncatechars:100 }}</p>
                                            <div>
                                                <small class="text-muted">Task ID: {{ task.task_id }}</small>
                                                <small class="text-muted ms-3">Order: {{ task.order }}</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="modal" data-bs-target="#editTaskModal{{ task.id }}">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteTaskModal{{ task.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-list-check" style="font-size: 3rem;"></i>
                </div>
                <h4>No Tasks Yet</h4>
                <p class="text-muted">Add tasks to your course to get started.</p>
                <button type="button" class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                    <i class="bi bi-plus-circle"></i> Add First Task
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">Add New Task</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Task form fields will go here -->
                    <div class="mb-3">
                        <label for="{{ task_form.title.id_for_label }}" class="form-label">Task Title</label>
                        {{ task_form.title }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ task_form.description.id_for_label }}" class="form-label">Description</label>
                        {{ task_form.description }}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ task_form.role.id_for_label }}" class="form-label">Role</label>
                            {{ task_form.role }}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ task_form.task_id.id_for_label }}" class="form-label">Task ID</label>
                            {{ task_form.task_id }}
                            <div class="form-text">Unique identifier for this task</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ task_form.order.id_for_label }}" class="form-label">Order</label>
                        {{ task_form.order }}
                        <div class="form-text">Order within the role (lower numbers appear first)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ task_form.challenge_text.id_for_label }}" class="form-label">Challenge Text</label>
                        {{ task_form.challenge_text }}
                        <div class="form-text">The challenge text presented to the user</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ task_form.success_criteria.id_for_label }}" class="form-label">Success Criteria</label>
                        {{ task_form.success_criteria }}
                        <div class="form-text">Criteria for successful completion</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Task</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Course Modal -->
<div class="modal fade" id="editCourseModal" tabindex="-1" aria-labelledby="editCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post" action="{% url 'game:company_course_detail' company_slug=company.slug course_id=course.id %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit_course">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCourseModalLabel">Edit Course</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Course form fields will go here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize form elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to task form elements
        const formElements = [
            '{{ task_form.title.id_for_label }}',
            '{{ task_form.description.id_for_label }}',
            '{{ task_form.task_id.id_for_label }}',
            '{{ task_form.order.id_for_label }}',
            '{{ task_form.challenge_text.id_for_label }}',
            '{{ task_form.success_criteria.id_for_label }}'
        ];
        
        formElements.forEach(function(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                if (element.tagName === 'TEXTAREA') {
                    element.classList.add('form-control');
                } else if (element.tagName === 'SELECT') {
                    element.classList.add('form-select');
                } else {
                    element.classList.add('form-control');
                }
            }
        });
    });
</script>
{% endblock %}
