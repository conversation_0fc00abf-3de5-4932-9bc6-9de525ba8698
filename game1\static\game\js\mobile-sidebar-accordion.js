// Mobile Sidebar Accordion
// Makes entire sidebars function as single accordion panels on mobile
(function () {
    function setupMobileSidebarAccordion() {
        console.log('[Mobile Sidebar] running setup, viewport width:', window.innerWidth);

        if (window.innerWidth > 992) {
            // On larger screens, ensure sidebars are visible and reset any mobile-specific styling
            const leftSidebar = document.querySelector('.sidebar');
            const rightSidebar = document.querySelector('.right-sidebar');

            if (leftSidebar) {
                leftSidebar.style.maxHeight = '';
                leftSidebar.style.overflow = '';
                leftSidebar.style.display = '';
            }

            if (rightSidebar) {
                rightSidebar.style.maxHeight = '';
                rightSidebar.style.overflow = '';
                rightSidebar.style.display = '';
            }

            // Remove mobile body classes
            document.body.classList.remove('sidebar-visible', 'right-sidebar-visible');
            return;
        }

        // On mobile, ensure sidebars start collapsed
        const leftSidebar = document.querySelector('.sidebar');
        const rightSidebar = document.querySelector('.right-sidebar');

        if (leftSidebar) {
            // Reset any individual section accordion styling
            leftSidebar.querySelectorAll('.mb-4 ul').forEach(function(ul) {
                ul.style.display = '';
                ul.style.maxHeight = '';
                ul.style.overflow = '';
            });

            leftSidebar.querySelectorAll('h6, .sidebar-heading').forEach(function(header) {
                header.style.cursor = '';
                header.style.background = '';
                header.style.padding = '';
                header.style.borderRadius = '';
                header.style.marginBottom = '';
                header.removeAttribute('aria-expanded');

                // Remove any accordion indicators
                const indicator = header.querySelector('.accordion-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
        }

        if (rightSidebar) {
            // Reset any individual section accordion styling
            rightSidebar.querySelectorAll('.mb-4 ul').forEach(function(ul) {
                ul.style.display = '';
                ul.style.maxHeight = '';
                ul.style.overflow = '';
            });

            rightSidebar.querySelectorAll('h6, .sidebar-heading').forEach(function(header) {
                header.style.cursor = '';
                header.style.background = '';
                header.style.padding = '';
                header.style.borderRadius = '';
                header.style.marginBottom = '';
                header.removeAttribute('aria-expanded');

                // Remove any accordion indicators
                const indicator = header.querySelector('.accordion-indicator');
                if (indicator) {
                    indicator.remove();
                }
            });
        }

        // Ensure sidebars start collapsed on mobile
        document.body.classList.remove('sidebar-visible', 'right-sidebar-visible');
    }

    // Setup mobile sidebar toggle buttons in header
    function setupMobileSidebarToggles() {
        if (window.innerWidth > 992) return;

        // Ensure toggle buttons are visible and properly positioned
        const leftToggle = document.getElementById('sidebar-toggle');
        const rightToggle = document.getElementById('right-sidebar-toggle');

        if (leftToggle) {
            leftToggle.style.display = 'flex';
            leftToggle.style.visibility = 'visible';
            leftToggle.style.opacity = '1';
            leftToggle.style.zIndex = '1050';
        }

        if (rightToggle) {
            rightToggle.style.display = 'flex';
            rightToggle.style.visibility = 'visible';
            rightToggle.style.opacity = '1';
            rightToggle.style.zIndex = '1050';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        setupMobileSidebarAccordion();
        setupMobileSidebarToggles();
    });

    window.addEventListener('resize', function() {
        setupMobileSidebarAccordion();
        setupMobileSidebarToggles();
    });
})();
