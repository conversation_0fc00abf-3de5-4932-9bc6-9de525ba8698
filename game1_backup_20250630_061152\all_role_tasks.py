# All Role Tasks for Rwenzori Innovations Ltd.
# This file contains task definitions for all roles in the company hierarchy

import os
import json

# Define what should be exported from this module
__all__ = ['get_all_role_tasks']

# Define the directory where role JSON files are stored.
# This script assumes the 'roles' directory is in the same directory as this file.
ROLES_DIR = "roles" # This is the name of the subdirectory
ALL_ROLE_TASKS = {}

# Define the mapping of role managers to game characters
# This ensures compatibility with the CHARACTERS dictionary in context_aware_game.py
# Updated for the simplified 10-role progression from GAME_ARCHITECTURE_GUIDE.md
MANAGER_MAPPING = {
    # Simplified progression roles - each role reports to the next level up
    "junior_assistant": "manager",
    "sales_associate": "manager",
    "marketing_associate": "manager",
    "senior_marketing_specialist": "vp",
    "marketing_manager": "vp",
    "senior_marketing_manager": "vp_marketing",
    "marketing_director": "vp_marketing",
    "vp_marketing": "coo",
    "cmo": "board",

    # Legacy mappings for backward compatibility (if needed)
    "sales_manager": "vp_marketing",
    "advertising_manager": "vp_marketing",
    "service_manager": "vp_operations",
    "production_manager": "vp_operations",
    "facilities_manager": "vp_operations",
    "accounts_receivable_manager": "vp_finance",
    "accounts_payable_manager": "vp_finance",
    "hr_coordinator": "hr",
    "hr_manager": "hr_director"
}

# Construct the absolute path to the roles directory
# os.path.abspath(__file__) gives the absolute path of the current script
# os.path.dirname(...) gives the directory containing the current script
base_dir = os.path.dirname(os.path.abspath(__file__))
roles_abs_dir = os.path.join(base_dir, ROLES_DIR)

# Load all role tasks from JSON files in the roles directory
if os.path.exists(roles_abs_dir) and os.path.isdir(roles_abs_dir):
    for filename in os.listdir(roles_abs_dir):
        if filename.endswith(".json"):
            role_name = filename[:-5]  # Remove .json extension
            filepath = os.path.join(roles_abs_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f: # Added encoding
                    role_tasks = json.load(f)

                    # Update manager references to ensure compatibility
                    for task in role_tasks:
                        # Map manager references to compatible characters
                        if "manager" in task and task["manager"] in MANAGER_MAPPING:
                            task["manager"] = MANAGER_MAPPING[task["manager"]]

                    ALL_ROLE_TASKS[role_name] = role_tasks
            except json.JSONDecodeError as jde: # Specific error for JSON decoding
                print(f"Warning: Could not decode JSON from {filepath}. Error: {jde}")
            except IOError as ioe: # Specific error for I/O problems
                print(f"Warning: Could not read file {filepath}. Error: {ioe}")
            except Exception as e: # General fallback for other unexpected errors
                print(f"Warning: An unexpected error occurred while loading {filepath}: {e}")
else:
    # Warning if the roles directory itself is not found
    print(f"Warning: Roles directory '{roles_abs_dir}' not found. No roles will be loaded from JSON files.")

# Function to get tasks for a specific role
def get_all_role_tasks(role):
    """Get tasks for a specific role from the ALL_ROLE_TASKS dictionary.

    Args:
        role (str): The role to get tasks for

    Returns:
        list: A list of tasks for the specified role
    """
    if role in ALL_ROLE_TASKS:
        # Return the pre-loaded tasks for this role
        return ALL_ROLE_TASKS[role]
    else:
        # For roles not explicitly defined, create generic tasks
        role_display = role.replace("_", " ").title()

        # Determine the appropriate manager based on role
        # Use the MANAGER_MAPPING dictionary first
        if role in MANAGER_MAPPING:
            manager = MANAGER_MAPPING[role]
        # For roles not in the mapping, use these default assignments
        elif role in ["vp_marketing", "vp_operations", "vp_finance"]:
            manager = "coo"
        elif role == "coo":
            manager = "ceo"
        elif role == "ceo":
            manager = "board"
        elif role == "cmo":
            manager = "board"  # CMO reports to board
        elif role == "shareholders":
            manager = "board"
        else:
            # Default to manager for unknown roles
            manager = "manager"

        # Create 3 generic tasks for this role with detailed conversational elements
        # following the style of applicant and junior_assistant roles
        generic_tasks = []

        # Task titles for variety
        task_titles = [
            "Strategic Planning Initiative",
            "Team Optimization Project",
            "Performance Enhancement Analysis"
        ]

        # Opening greetings for variety
        greetings = [
            f"Hello there! Hope you're having a great day.",
            f"Good morning! I've been impressed with your work so far.",
            f"Hi! I'm glad to see you're settling into your role nicely."
        ]

        # Closing statements for variety
        closings = [
            "I'm looking forward to seeing your creative approach on this one!",
            "I know you'll bring some fresh insights to this challenge. Can't wait to see what you come up with!",
            "This is your chance to really shine. I have confidence in your abilities!"
        ]

        for i in range(3):
            task = {
                "id": f"task_{i+1}_for_{role}",
                "manager": manager,
                "description": f"{greetings[i]} I have an important {task_titles[i]} that needs your attention. As our {role_display}, you're in a unique position to tackle this effectively. I need you to analyze the current situation, identify key opportunities for improvement, and develop a comprehensive plan that aligns with our organizational goals. This is a critical responsibility that will showcase your strategic thinking and attention to detail. {closings[i]}",
                "response_template": f"""## {role_display} Task {i+1} Response

**Executive Summary:**
A comprehensive analysis and strategic approach to addressing the requirements for this {role_display} task.

**Key Points:**
- Strategic consideration #1
- Important factor #2
- Critical element #3
- Implementation approach #4

**Detailed Plan:**
[Detailed explanation of the approach, methodology, and implementation steps]

**Expected Outcomes:**
- Primary benefit
- Secondary advantage
- Long-term impact

**Conclusion:**
This solution addresses all requirements while aligning with our organizational goals and values.""",
                "feedback": {
                    "good": f"Wow! I'm genuinely impressed with your work on this {task_titles[i].lower()}! 😊 You've really knocked it out of the park. Your strategic approach shows you've got a natural talent for this kind of work, and the innovative solutions you've proposed are exactly what we need. I especially loved how you considered both the immediate impact and long-term benefits. This is the kind of forward thinking that makes a real difference at Rwenzori Innovations. Keep up this excellent work - you're definitely on the right track for growth here!",
                    "okay": f"Thanks for your work on this {task_titles[i].lower()}. You've done a good job addressing the core requirements, and I can see you've got a solid understanding of the basics. I think with a bit more experience, you'll really excel at this. For your next task, try digging a little deeper with your analysis and don't be afraid to propose more innovative approaches. I see a lot of potential in you, and I'm looking forward to watching you grow in this role. Let me know if you'd like to discuss any specific areas where you'd like more guidance!",
                    "bad": f"Thank you for your effort on this {task_titles[i].lower()}. I appreciate that you've taken time to work on this, but I think we need to have a conversation about the expectations for someone in the {role_display} position. Your response needs more depth and strategic thinking to meet our standards. Don't worry though - everyone has areas where they can improve! Let's set up some time to talk through this together. I'd be happy to provide more guidance on what we're looking for and help you develop the skills you need to succeed in this role. What time works for you to discuss this further?"
                }
            }

            generic_tasks.append(task)

        return generic_tasks
