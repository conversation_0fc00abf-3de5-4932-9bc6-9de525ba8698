"""
Utility functions for the Context-Aware Game.
This module contains helper functions used throughout the game.
"""

import uuid
import datetime
import markdown
import logging

def get_task_by_id(task_id, tasks_list):
    """
    Get a task by its ID from the tasks list.

    Args:
        task_id (str): The ID of the task to find
        tasks_list (list): The list of tasks to search

    Returns:
        dict or None: The task with the matching ID, or None if not found
    """
    for task in tasks_list:
        if task.get("id") == task_id:
            return task
    return None

def create_message(sender, text, is_challenge=False, task_id=None, is_markdown=True, is_promotion=False):
    """
    Create a message object for the game state.
    
    Args:
        sender (str): The sender of the message
        text (str): The text content of the message
        is_challenge (bool, optional): Whether this is a challenge message. Defaults to False.
        task_id (str, optional): The ID of the task if this is a challenge. Defaults to None.
        is_markdown (bool, optional): Whether the text is in markdown format. Defaults to True.
        is_promotion (bool, optional): Whether this is a promotion message. Defaults to False.
        
    Returns:
        dict: The message object
    """
    # Convert markdown to HTML if needed
    html = markdown.markdown(text, extensions=['extra']) if is_markdown else text
    
    # Create the message object
    message = {
        "id": str(uuid.uuid4()),
        "sender": sender,
        "text": text,
        "html": html,
        "timestamp": datetime.datetime.now().isoformat(),
        "is_challenge": is_challenge,
        "is_markdown": is_markdown
    }
    
    # Add task_id if provided
    if task_id:
        message["task_id"] = task_id
        
    # Add is_promotion if true
    if is_promotion:
        message["is_promotion"] = True
        
    return message

def create_delayed_message(sender, text, delay_seconds=5, is_challenge=False, task_id=None, is_markdown=True):
    """
    Create a message with a delayed timestamp.
    
    Args:
        sender (str): The sender of the message
        text (str): The text content of the message
        delay_seconds (int, optional): The delay in seconds. Defaults to 5.
        is_challenge (bool, optional): Whether this is a challenge message. Defaults to False.
        task_id (str, optional): The ID of the task if this is a challenge. Defaults to None.
        is_markdown (bool, optional): Whether the text is in markdown format. Defaults to True.
        
    Returns:
        dict: The message object with a delayed timestamp
    """
    # Create a basic message
    message = create_message(sender, text, is_challenge, task_id, is_markdown)
    
    # Update the timestamp with a delay
    delayed_time = datetime.datetime.now() + datetime.timedelta(seconds=delay_seconds)
    message["timestamp"] = delayed_time.isoformat()
    
    return message

def log_debug(message, data=None):
    """
    Log a debug message with optional data.
    
    Args:
        message (str): The message to log
        data (any, optional): Additional data to log. Defaults to None.
    """
    if data:
        logging.debug(f"{message}: {data}")
        print(f"\n\n{message}: {data}\n\n")
    else:
        logging.debug(message)
        print(f"\n\n{message}\n\n")

def log_info(message, data=None):
    """
    Log an info message with optional data.
    
    Args:
        message (str): The message to log
        data (any, optional): Additional data to log. Defaults to None.
    """
    if data:
        logging.info(f"{message}: {data}")
        print(f"\n\n{message}: {data}\n\n")
    else:
        logging.info(message)
        print(f"\n\n{message}\n\n")

def log_error(message, error=None):
    """
    Log an error message with optional error details.
    
    Args:
        message (str): The message to log
        error (Exception, optional): The error to log. Defaults to None.
    """
    if error:
        error_message = f"{message}: {str(error)}"
        logging.error(error_message)
        print(f"\n\nERROR: {error_message}\n\n")
    else:
        logging.error(message)
        print(f"\n\nERROR: {message}\n\n")

def get_relevant_context(game_state, prompt):
    """
    Get the relevant context for the current task.
    This focuses on the current manager and task rather than the entire conversation history.
    
    Args:
        game_state (dict): The current game state
        prompt (str): The user's prompt
        
    Returns:
        list: The relevant messages for context
    """
    relevant_messages = []
    current_task = game_state["current_task"]
    current_manager = game_state["current_manager"]

    logging.info(f"Getting relevant context for task: {current_task} with manager: {current_manager}")

    # First, find the task introduction message from the current manager
    task_intro = None
    for message in reversed(game_state["messages"]):
        if message.get("is_challenge") and message.get("sender") == current_manager:
            task_intro = message
            break

    if task_intro:
        relevant_messages.append(task_intro)
        logging.info(f"Added task intro from {current_manager} to context")

    # Add any messages between the user and the current manager after the task intro
    if task_intro:
        task_intro_index = game_state["messages"].index(task_intro)
        for message in game_state["messages"][task_intro_index+1:]:
            if message.get("sender") in [current_manager, "user"]:
                relevant_messages.append(message)
                logging.info(f"Added message from {message.get('sender')} to context")

    # Add the current prompt
    relevant_messages.append({
        "sender": "user",
        "text": prompt
    })

    logging.info(f"Final context has {len(relevant_messages)} messages")
    return relevant_messages
