from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_role', models.CharField(default='applicant', max_length=50)),
                ('performance_score', models.IntegerField(default=0)),
                ('challenges_completed', models.IntegerField(default=0)),
                ('role_challenges_completed', models.IntegerField(default=0)),
                ('game_completed', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('current_manager', models.CharField(default='hr', max_length=50)),
                ('current_task', models.CharField(default='cover_letter', max_length=50)),
                ('completed_roles', models.TextField(default='[]')),
                ('first_task_pending', models.BooleanField(default=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='auth.user')),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_id', models.CharField(max_length=100)),
                ('sender', models.CharField(max_length=50)),
                ('text', models.TextField()),
                ('html', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('is_challenge', models.BooleanField(default=False)),
                ('is_markdown', models.BooleanField(default=True)),
                ('task_id', models.CharField(blank=True, max_length=50, null=True)),
                ('game_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='game.gamesession')),
            ],
        ),
    ]
