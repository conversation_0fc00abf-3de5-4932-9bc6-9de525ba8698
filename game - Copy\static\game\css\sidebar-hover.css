/* Sidebar Hover Styles */

.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 65px; /* Initial collapsed width */
    background: linear-gradient(135deg, #4a86e8 0%, #7b1fa2 100%); /* Purple gradient like main header */
    color: white;
    z-index: 1000; /* Ensure it's above other content */
    overflow-x: hidden; /* Hide content that overflows horizontally */
    white-space: nowrap; /* Prevent text wrapping */
    transition: width 0.3s ease-in-out;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    /* padding-top removed as sidebar now starts at viewport top */
}

.sidebar:hover {
    width: 250px; /* Expanded width */
}

/* Hide sidebar header/brand when collapsed, show on hover */
.sidebar .sidebar-header {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    height: 0; /* Collapse height */
    overflow: hidden;
}

.sidebar:hover .sidebar-header {
    opacity: 1;
    height: auto; /* Restore height */
    padding: 1rem; /* Add padding back */
}

.sidebar .sidebar-brand {
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center; /* Center icon when collapsed */
}

.sidebar:hover .sidebar-brand {
   justify-content: flex-start; /* Align left when expanded */
}

.sidebar .sidebar-brand i {
    font-size: 1.5rem;
    margin-right: 0; /* No margin when collapsed */
    transition: margin-right 0.3s ease-in-out;
}
.sidebar:hover .sidebar-brand i {
    margin-right: 10px; /* Add margin when expanded */
}

.sidebar .sidebar-brand span {
    display: none; /* Hide text when collapsed */
}

.sidebar:hover .sidebar-brand span {
    display: inline; /* Show text when expanded */
}


/* Style navigation links */
.sidebar .sidebar-menu {
    padding-top: 1rem;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.85);
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem; /* Adjust padding */
    text-decoration: none;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar .nav-link i {
    font-size: 1.2rem;
    width: 30px; /* Fixed width for icon alignment */
    text-align: center;
    margin-right: 0; /* No margin when collapsed */
    transition: margin-right 0.3s ease-in-out;
}

.sidebar:hover .nav-link i {
     margin-right: 15px; /* Add space between icon and text when expanded */
}

.sidebar .nav-link span {
    display: none; /* Hide text initially */
    opacity: 0;
    transition: opacity 0.2s ease-in-out 0.1s; /* Delay opacity transition */
}

.sidebar:hover .nav-link span {
    display: inline; /* Show text on hover */
    opacity: 1;
}

/* Dividers and Headers */
.sidebar .nav-divider {
    margin: 0.5rem 1.25rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0; /* Hide when collapsed */
    transition: opacity 0.2s ease-in-out;
}
.sidebar:hover .nav-divider {
    opacity: 1;
}

.sidebar .nav-header {
    padding: 0.5rem 1.25rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    opacity: 0; /* Hide when collapsed */
    transition: opacity 0.2s ease-in-out;
}
.sidebar:hover .nav-header {
    opacity: 1;
}

/* Adjust main content padding when sidebar is present */
/* You might need to adjust the selector based on your main content container */
body {
    padding-left: 65px; /* Match initial sidebar width */
    transition: padding-left 0.3s ease-in-out;
}

/* Optional: Add a class to body when sidebar hovers if needed for complex adjustments */
/* body.sidebar-expanded { padding-left: 250px; } */

/* Dark mode adjustments (optional, copy if needed) */
/* html.dark-mode .sidebar, body.dark-mode .sidebar { ... } */