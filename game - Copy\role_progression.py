"""
Role progression structure for the Context-Aware Game.
This module contains the role progression structure and related functions.
"""

# Role progression structure
ROLE_PROGRESSION = {
    # Entry Level
    "applicant": {
        "next_role": "junior_assistant",
        "challenges_required": 1,  # Applicant only needs 1 task (cover letter) to advance
        "promotion_message": "Congratulations! Your excellent performance has earned you a position as a Junior Assistant at Rwenzori Innovations Ltd. You're now officially part of the team!"
    },
    "junior_assistant": {
        "next_role": "sales_associate",
        "challenges_required": 3,  # All roles (except applicant) require exactly 3 tasks
        "promotion_message": "Great work! You've been promoted to Sales Associate in our Marketing department. Your communication skills will be valuable in this role!"
    },

    # Marketing Department
    "sales_associate": {
        "next_role": "marketing_associate",
        "challenges_required": 3,
        "promotion_message": "Your sales skills have impressed us! You're now being promoted to Marketing Associate where you'll help develop our marketing strategies."
    },
    "marketing_associate": {
        "next_role": "service_associate",
        "challenges_required": 3,
        "promotion_message": "Your marketing expertise has been noticed! You're now moving to Operations as a Service Associate to broaden your experience across departments."
    },
    # Operations Department - Associate Level
    "service_associate": {
        "next_role": "production_associate",
        "challenges_required": 3,
        "promotion_message": "Your customer service skills have been noticed! You're now moving to Production Associate to learn about our manufacturing processes."
    },
    "production_associate": {
        "next_role": "facilities_associate",
        "challenges_required": 3,
        "promotion_message": "Your attention to detail in production has earned you a position as Facilities Associate where you'll help maintain our operational infrastructure."
    },
    "facilities_associate": {
        "next_role": "accounts_receivable_associate",
        "challenges_required": 3,
        "promotion_message": "Your facilities management skills have impressed us! You're now moving to Finance as an Accounts Receivable Associate to broaden your experience."
    },
    # Finance Department - Associate Level
    "accounts_receivable_associate": {
        "next_role": "accounts_payable_associate",
        "challenges_required": 3,
        "promotion_message": "Your financial skills have been noticed! You're now moving to Accounts Payable Associate to expand your financial knowledge."
    },
    "accounts_payable_associate": {
        "next_role": "hr_coordinator",
        "challenges_required": 3,
        "promotion_message": "Your financial acumen is noted! Now, to broaden your corporate experience, you're moving into an HR Coordinator role. This will give you valuable insight into our people operations."
    },
    "hr_coordinator": {
        "next_role": "sales_manager",
        "challenges_required": 3,
        "promotion_message": "Your contributions as HR Coordinator have been excellent! You've shown a real talent for people-related tasks. Now, you're ready to take on the challenges of a Sales Manager in our Marketing department!"
    },
    # Management Level - Marketing Department
    "sales_manager": {
        "next_role": "advertising_manager",
        "challenges_required": 3,
        "promotion_message": "Excellent leadership! You've been promoted to Advertising Manager to oversee our advertising strategies."
    },
    "advertising_manager": {
        "next_role": "service_manager",
        "challenges_required": 3,
        "promotion_message": "Your success in advertising has earned you a promotion to Service Manager in our Operations department!"
    },
    # Management Level - Operations Department
    "service_manager": {
        "next_role": "production_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in service has earned you a promotion to Production Manager. You'll now oversee our manufacturing operations!"
    },
    "production_manager": {
        "next_role": "facilities_manager",
        "challenges_required": 3,
        "promotion_message": "Excellent work managing production! You've been promoted to Facilities Manager to oversee all our facilities operations."
    },
    "facilities_manager": {
        "next_role": "accounts_receivable_manager",
        "challenges_required": 3,
        "promotion_message": "Your operational leadership has earned you a promotion to Accounts Receivable Manager in our Finance department!"
    },
    # Management Level - Finance Department
    "accounts_receivable_manager": {
        "next_role": "accounts_payable_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in accounts receivable has earned you a promotion to Accounts Payable Manager."
    },
    "accounts_payable_manager": {
        "next_role": "hr_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in finance has been outstanding. To further develop your executive capabilities, you're taking on the role of HR Manager. This strategic position will be key to shaping our company culture and talent strategy."
    },
    "hr_manager": {
        "next_role": "vp_marketing",
        "challenges_required": 3,
        "promotion_message": "Your strategic leadership as HR Manager has significantly impacted our organization! You're now ready to step into the role of Vice-President of Marketing. Congratulations on this major executive promotion!"
    },
    # VP Level
    "vp_marketing": {
        "next_role": "vp_operations",
        "challenges_required": 3,
        "promotion_message": "Your success as VP of Marketing has earned you a promotion to Vice-President of Operations!"
    },
    "vp_operations": {
        "next_role": "vp_finance",
        "challenges_required": 3,
        "promotion_message": "Your success as VP of Operations has earned you a promotion to Vice-President of Finance!"
    },
    "vp_finance": {
        "next_role": "coo",
        "challenges_required": 3,
        "promotion_message": "Your exceptional leadership across all departments has earned you a promotion to Chief Operating Officer (COO)!"
    },
    # Executive Level
    "coo": {
        "next_role": "ceo",
        "challenges_required": 3,
        "promotion_message": "Your outstanding performance as COO has impressed the board! You've been promoted to Chief Executive Officer (CEO)."
    },
    "ceo": {
        "next_role": "shareholders",
        "challenges_required": 3,
        "promotion_message": "After your successful tenure as CEO, you've been invited to join the Shareholders group at the very top of Rwenzori Innovations Ltd.!"
    },
    "shareholders": {
        "next_role": None,
        "challenges_required": 3,
        "promotion_message": "Congratulations! You've reached the pinnacle of Rwenzori Innovations Ltd. as a Shareholder. The company is thriving under your leadership and ownership!"
    }
}

def get_next_role(current_role):
    """
    Get the next role in the progression.

    Args:
        current_role (str): The current role

    Returns:
        str or None: The next role or None if there is no next role
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    return role_info.get("next_role")

def get_challenges_required(current_role):
    """
    Get the number of challenges required to advance from the current role.

    Args:
        current_role (str): The current role

    Returns:
        int: The number of challenges required
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    return role_info.get("challenges_required", 3)

def get_promotion_message(current_role):
    """
    Get the promotion message for advancing from the current role.

    Args:
        current_role (str): The current role

    Returns:
        str: The promotion message with enhanced formatting
    """
    role_info = ROLE_PROGRESSION.get(current_role, {})
    base_message = role_info.get("promotion_message", f"Congratulations! You've been promoted to {get_next_role(current_role)}!")

    # Add markdown formatting to make the message more prominent
    # This matches the Flask app's style
    enhanced_message = f"""## 🎉 Congratulations! 🎉

{base_message}

### How to Improve Your Request:

#### Try these tips:
- Be specific about what you want in your next task
- Provide context about your situation
- Specify the format you prefer for the response
"""

    return enhanced_message

def get_role_info(role):
    """
    Get information about a role.

    Args:
        role (str): The role to get information about

    Returns:
        dict: The role information or an empty dict if not found
    """
    return ROLE_PROGRESSION.get(role, {})
