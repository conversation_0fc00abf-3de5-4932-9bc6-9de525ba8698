// Task definitions for Rwenzori Innovations career progression game
// Each position has 3 tasks that must be completed to advance

const TASK_DEFINITIONS = {
    // Entry level tasks
    applicant: [
        {
            id: "cover_letter",
            title: "Create a Cover Letter",
            description: "Write a compelling cover letter for your application to Rwenzori Innovations",
            instructions: "Create a professional cover letter addressing your interest in joining Rwenzori Innovations Highlight your relevant skills and experience, and explain why you would be a good fit for the company.",
            feedback: {
                good: "Excellent cover letter! Your writing is clear and professional, and you've effectively highlighted your relevant skills and experience. We're impressed with your attention to detail.",
                okay: "Good effort on your cover letter. You've included most of the key elements, but there's room for improvement in how you present your skills and experience. Consider being more specific about your qualifications.",
                bad: "Your cover letter needs significant improvement. It lacks professionalism and doesn't effectively highlight your qualifications. Please revise it to better showcase your skills and experience."
            }
        },
        {
            id: "resume",
            title: "Update Your Resume",
            description: "Tailor your resume to highlight skills relevant to Rwenzori Innovations",
            instructions: "Review and update your resume to emphasize skills and experience relevant to working at Rwenzori Innovations Focus on clarity, organization, and highlighting achievements rather than just listing responsibilities.",
            feedback: {
                good: "Your resume is excellent! It's well-organized, highlights your relevant skills and achievements, and presents your qualifications clearly. This is exactly what we're looking for.",
                okay: "Your resume is good but could be stronger. Consider reorganizing some sections and focusing more on achievements rather than just listing responsibilities. Make sure your most relevant skills are prominently featured.",
                bad: "Your resume needs significant work. It's disorganized, lacks focus on relevant skills, and doesn't effectively showcase your qualifications. Please revise it to better highlight your relevant experience and achievements."
            }
        },
        {
            id: "interview",
            title: "Complete the Interview",
            description: "Successfully answer interview questions to join Rwenzori Innovations",
            instructions: "Prepare for your interview by researching Rwenzori Innovations and practicing answers to common interview questions. Be ready to discuss your skills, experience, and how you would contribute to the company.",
            feedback: {
                good: "Congratulations! You performed excellently in the interview. Your answers were thoughtful and demonstrated both your qualifications and your understanding of our company. We're pleased to offer you a position in our HR department.",
                okay: "You did well in the interview, though there were some areas where your answers could have been stronger. However, we see potential in you and would like to offer you a position in our HR department.",
                bad: "Unfortunately, your interview performance didn't meet our expectations. Your answers lacked depth and didn't effectively demonstrate your qualifications. However, we're willing to give you another chance with our HR department."
            }
        }
    ],
    
    // HR Department
    hr_associate: [
        {
            id: "onboarding_checklist",
            title: "Create Onboarding Checklist",
            description: "Develop a comprehensive onboarding checklist for new employees.",
            instructions: "Create a detailed onboarding checklist that covers a new employee's first week at Rwenzori Innovations Include administrative tasks, training sessions, introductions, and other elements to help new hires acclimate to the company.",
            feedback: {
                good: "Excellent work on the onboarding checklist! It's comprehensive, well-organized, and covers all the essential elements for a new employee's first week. This will be very helpful for our HR department.",
                okay: "Good effort on the onboarding checklist. You've included most of the important elements, but there are some gaps in the process. Consider adding more detail about training sessions and departmental introductions.",
                bad: "The onboarding checklist needs significant improvement. It's missing several critical elements and lacks organization. Please revise it to be more comprehensive and better structured."
            }
        },
        {
            id: "employee_handbook",
            title: "Update Employee Handbook",
            description: "Update the remote work policy in the employee handbook.",
            instructions: "Review and update the remote work policy in the employee handbook to reflect current best practices. Consider flexibility, productivity expectations, communication requirements, and technology needs.",
            feedback: {
                good: "Your update to the remote work policy is excellent! It's clear, comprehensive, and balances employee flexibility with company needs. The policy is well-structured and addresses all key aspects of remote work.",
                okay: "Your update to the remote work policy is good but could be improved. Some sections lack clarity, and there are a few important elements missing, such as specific communication requirements and technology support.",
                bad: "The updated remote work policy needs significant revision. It's unclear, lacks important details, and doesn't effectively balance employee flexibility with company needs. Please rework it to be more comprehensive and clear."
            }
        },
        {
            id: "training_program",
            title: "Design Training Program",
            description: "Create a training program for new hires in the HR department.",
            instructions: "Design a comprehensive training program for new HR associates. Include modules on company policies, HR software systems, employee relations, recruitment processes, and other essential HR functions.",
            feedback: {
                good: "Excellent training program design! It's comprehensive, well-structured, and covers all essential HR functions. The learning objectives are clear, and the program includes a good mix of theoretical knowledge and practical skills.",
                okay: "Good effort on the training program. You've included most of the important elements, but some modules could be more detailed. Consider adding more hands-on practice with HR software systems and recruitment processes.",
                bad: "The training program design needs significant improvement. It lacks structure, misses several critical HR functions, and doesn't provide clear learning objectives. Please revise it to be more comprehensive and better organized."
            }
        }
    ],
    
    // Marketing Department
    marketing_associate: [
        {
            id: "market_research",
            title: "Conduct Market Research",
            description: "Research market trends for solar panel products and compile findings.",
            instructions: "Conduct comprehensive market research on current trends in the solar panel industry. Analyze competitor products, identify market gaps, and compile your findings in a detailed report with actionable insights.",
            feedback: {
                good: "Excellent market research! Your analysis is thorough, well-supported with data, and provides valuable insights into market trends and opportunities. The actionable recommendations are particularly helpful.",
                okay: "Good effort on the market research. You've gathered useful information, but the analysis could be deeper and more focused on actionable insights. Consider providing more specific recommendations based on your findings.",
                bad: "The market research needs significant improvement. It lacks depth, contains limited data analysis, and doesn't provide clear insights or recommendations. Please revise it to be more comprehensive and actionable."
            }
        },
        {
            id: "social_media",
            title: "Create Social Media Strategy",
            description: "Develop a social media strategy to promote solar panel products.",
            instructions: "Create a comprehensive social media strategy for promoting our solar panel products. Include platform selection, content types, posting schedule, engagement tactics, and metrics for measuring success.",
            feedback: {
                good: "Excellent social media strategy! It's comprehensive, tailored to our products, and includes clear tactics for each platform. The content calendar and measurement metrics are particularly well-developed.",
                okay: "Good effort on the social media strategy. You've covered the basics, but some elements could be more detailed. Consider developing more specific content ideas and engagement tactics for each platform.",
                bad: "The social media strategy needs significant improvement. It's too generic, lacks platform-specific tactics, and doesn't include clear metrics for measuring success. Please revise it to be more comprehensive and tailored to our products."
            }
        },
        {
            id: "content_calendar",
            title: "Design Content Calendar",
            description: "Create a content calendar for upcoming marketing campaigns.",
            instructions: "Design a three-month content calendar for our upcoming solar panel marketing campaigns. Include blog posts, social media content, email newsletters, and other content types, with themes, topics, and publication dates.",
            feedback: {
                good: "Excellent content calendar! It's well-organized, comprehensive, and strategically aligns different content types across channels. The themes are cohesive and the scheduling is realistic.",
                okay: "Good effort on the content calendar. You've included a variety of content types, but the strategic alignment could be stronger. Consider how different content pieces can support each other across channels.",
                bad: "The content calendar needs significant improvement. It lacks organization, strategic alignment, and variety in content types. Please revise it to be more comprehensive and better structured."
            }
        }
    ],
    
    // Operations Department
    service_associate: [
        {
            id: "customer_service",
            title: "Improve Customer Service",
            description: "Develop strategies to enhance customer service for solar panel support.",
            instructions: "Create a plan to improve our customer service for solar panel support. Identify current pain points, propose solutions, and include training recommendations and metrics for measuring improvement.",
            feedback: {
                good: "Excellent customer service improvement plan! You've identified key pain points and proposed practical, effective solutions. The training recommendations and measurement metrics are particularly well-developed.",
                okay: "Good effort on the customer service plan. You've identified some important issues, but some of your proposed solutions could be more detailed. Consider adding more specific training recommendations and implementation steps.",
                bad: "The customer service improvement plan needs significant work. It doesn't effectively identify current problems or propose viable solutions. Please revise it to be more comprehensive and actionable."
            }
        },
        {
            id: "service_metrics",
            title: "Track Service Metrics",
            description: "Create a system for tracking and improving service metrics.",
            instructions: "Design a comprehensive system for tracking key customer service metrics for our solar panel support team. Include response times, resolution rates, customer satisfaction, and other relevant metrics, with processes for regular review and improvement.",
            feedback: {
                good: "Excellent service metrics system! It's comprehensive, well-structured, and includes all key performance indicators. The processes for review and continuous improvement are particularly strong.",
                okay: "Good effort on the service metrics system. You've included most of the important metrics, but the processes for review and improvement could be more detailed. Consider adding more specific action steps based on metric results.",
                bad: "The service metrics system needs significant improvement. It's missing several critical metrics and lacks clear processes for using the data to drive improvements. Please revise it to be more comprehensive and actionable."
            }
        },
        {
            id: "feedback_system",
            title: "Implement Feedback System",
            description: "Design a system for collecting and acting on customer feedback.",
            instructions: "Create a comprehensive system for collecting, analyzing, and acting on customer feedback about our solar panel products and support. Include multiple feedback channels, analysis methods, and processes for implementing improvements based on feedback.",
            feedback: {
                good: "Excellent feedback system design! It's comprehensive, includes multiple channels for collecting feedback, and has clear processes for analysis and action. The closed-loop approach ensures customers see the impact of their feedback.",
                okay: "Good effort on the feedback system. You've included several feedback channels, but the analysis and action processes could be more detailed. Consider adding more specific steps for turning feedback into improvements.",
                bad: "The feedback system design needs significant improvement. It lacks variety in feedback channels and doesn't have clear processes for analysis or action. Please revise it to be more comprehensive and actionable."
            }
        }
    ],
    
    // Executive Level
    coo: [
        {
            id: "operational_efficiency",
            title: "Improve Operational Efficiency",
            description: "Analyze and improve operational efficiency across all departments.",
            instructions: "Conduct a comprehensive analysis of operational workflows across Marketing, Operations, and Finance departments. Identify key bottlenecks and inefficiencies, and propose a strategic plan to improve overall operational efficiency by at least 15% within the next fiscal year.",
            feedback: {
                good: "Excellent operational efficiency analysis and plan! Your identification of cross-departmental bottlenecks is insightful, and your proposed solutions are practical and likely to achieve the target improvements. The implementation timeline and measurement metrics are particularly well-developed.",
                okay: "Good effort on the operational efficiency plan. You've identified some important inefficiencies, but your analysis could go deeper, especially regarding cross-departmental issues. Some of your proposed solutions need more detail on implementation steps and resource requirements.",
                bad: "The operational efficiency analysis and plan need significant improvement. Your identification of bottlenecks is superficial, and the proposed solutions lack specificity and practicality. Please revise with a more thorough analysis and more detailed, actionable recommendations."
            }
        },
        {
            id: "strategic_planning",
            title: "Strategic Planning",
            description: "Develop a strategic plan for company growth and expansion.",
            instructions: "Create a comprehensive five-year strategic plan for Rwenzori Innovations's growth and expansion. Include market analysis, competitive positioning, growth targets, new product development, market expansion opportunities, and resource requirements.",
            feedback: {
                good: "Excellent strategic plan! Your market analysis is thorough, and your growth strategy is both ambitious and realistic. The phased approach to expansion and new product development shows good understanding of our capabilities and market opportunities.",
                okay: "Good effort on the strategic plan. Your market analysis and growth targets are solid, but some aspects of the implementation strategy need more development. Consider adding more detail on resource requirements and risk mitigation strategies.",
                bad: "The strategic plan needs significant improvement. It lacks depth in market analysis, and the growth strategy is either too conservative or unrealistically ambitious. Please revise with more thorough research and a more balanced approach to growth planning."
            }
        },
        {
            id: "supply_chain_optimization",
            title: "Optimize Supply Chain",
            description: "Create a plan to optimize the solar panel supply chain.",
            instructions: "Develop a comprehensive plan to optimize our solar panel supply chain for greater resilience, cost efficiency, and sustainability. Analyze current vulnerabilities, identify improvement opportunities, and propose specific initiatives with implementation timelines and expected outcomes.",
            feedback: {
                good: "Excellent supply chain optimization plan! Your analysis of current vulnerabilities is thorough, and your proposed initiatives strike a good balance between resilience, cost efficiency, and sustainability. The implementation roadmap and ROI projections are particularly well-developed.",
                okay: "Good effort on the supply chain plan. You've identified some important improvement opportunities, but your analysis of current vulnerabilities could be more comprehensive. Some of your proposed initiatives need more detail on implementation steps and resource requirements.",
                bad: "The supply chain optimization plan needs significant improvement. Your analysis lacks depth, and the proposed initiatives are either too vague or impractical to implement. Please revise with a more thorough analysis and more specific, actionable recommendations."
            }
        }
    ]
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TASK_DEFINITIONS;
} else {
    window.TASK_DEFINITIONS = TASK_DEFINITIONS;
}
