{% extends 'game/company/base.html' %}

{% block title %}{{ company.name }} Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Company Dashboard</h1>
        <a href="{% url 'game:company_team' company_slug=company.slug %}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Invite Team Members
        </a>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-controller"></i>
                </div>
                <div class="value">{{ stats.total_sessions }}</div>
                <div class="label">Total Game Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
                <div class="value">{{ stats.active_sessions }}</div>
                <div class="label">Active Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="value">{{ stats.completed_sessions }}</div>
                <div class="label">Completed Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-star"></i>
                </div>
                <div class="value">{{ stats.average_score|floatformat:1 }}</div>
                <div class="label">Average Score</div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Top Performers -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Top Performers</h5>
                    <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for entry in top_performers %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-secondary me-2">{{ forloop.counter }}</span>
                                {{ entry.user.username }}
                                {% if entry.highest_role %}
                                <span class="badge bg-info ms-2">{{ entry.highest_role }}</span>
                                {% endif %}
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ entry.score }} pts</span>
                        </div>
                        {% empty %}
                        <div class="list-group-item text-center py-3">
                            <p class="mb-0 text-muted">No entries yet. Start playing to appear on the leaderboard!</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Sessions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Game Sessions</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for session in recent_sessions %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if session.user %}
                                    <strong>{{ session.user.username }}</strong>
                                    {% else %}
                                    <strong>Anonymous</strong>
                                    {% endif %}
                                    <small class="text-muted ms-2">{{ session.created_at|date:"M d, Y" }}</small>
                                </div>
                                <div>
                                    {% if session.game_completed %}
                                    <span class="badge bg-success">Completed</span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">In Progress</span>
                                    {% endif %}
                                    <span class="badge bg-primary ms-1">{{ session.performance_score }} pts</span>
                                </div>
                            </div>
                            <div class="mt-1 small">
                                <span class="text-muted">Current Role:</span> {{ session.current_role }}
                                {% if session.team %}
                                <span class="text-muted ms-3">Team:</span> {{ session.team }}
                                {% endif %}
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group-item text-center py-3">
                            <p class="mb-0 text-muted">No game sessions yet.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Courses -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Company Courses</h5>
                    <a href="{% url 'game:company_courses' company_slug=company.slug %}" class="btn btn-sm btn-outline-primary">Manage Courses</a>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for course in courses %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ course.name }}</h6>
                                    <p class="mb-0 small text-muted">
                                        {% if course.description %}
                                        {{ course.description|truncatechars:100 }}
                                        {% else %}
                                        No description provided.
                                        {% endif %}
                                    </p>
                                </div>
                                <div>
                                    {% if course.is_active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                    
                                    <a href="{% url 'game:company_course_detail' company_slug=company.slug course_id=course.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group-item text-center py-3">
                            <p class="mb-0 text-muted">No courses created yet.</p>
                            <a href="{% url 'game:company_courses' company_slug=company.slug %}" class="btn btn-primary btn-sm mt-2">
                                Create Your First Course
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- QR Code for Company Registration -->
    <div class="row mt-4">
        <div class="col-md-6 mx-auto">
            <div class="card text-center">
                <div class="card-header">
                    <h5 class="mb-0">Company Registration QR Code</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Scan this QR code to join {{ company.name }}.</p>
                    <div class="qr-code-container my-3">
                        <img src="{{ qr_code }}" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                    </div>
                    <p class="small text-muted">
                        Or share this link:
                        <a href="/game/company/{{ company.slug }}/join/">
                            /game/company/{{ company.slug }}/join/
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
