/**
 * Professional Mobile Interface for Corporate Prompt Master
 * Mobile-first responsive design with professional UX patterns
 *
 * IMPORTANT: This file overrides all existing mobile styles
 * Load this CSS file LAST to ensure proper precedence
 */

/* ===== MOBILE/DESKTOP INTERFACE CONTROL ===== */
/* Default: Hide mobile interface, show desktop */
.mobile-interface {
    display: none !important;
}

.desktop-interface {
    display: block !important;
}

/* Ensure desktop interface is not affected by mobile styles */
@media (min-width: 993px) {
    .desktop-interface {
        display: block !important;
    }

    .mobile-interface {
        display: none !important;
    }

    /* Ensure desktop layout is preserved */
    .desktop-interface .app-container {
        display: flex !important;
        min-height: 100vh !important;
        max-width: 1700px !important;
        margin: 0 auto !important;
        background: var(--bg-primary, #ffffff) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .desktop-interface .sidebar {
        width: 280px !important;
        min-width: 280px !important;
        max-width: 280px !important;
        display: flex !important;
        flex-direction: column !important;
        background: linear-gradient(180deg, var(--bg-secondary, #f8f9fa) 0%, var(--bg-tertiary, #e9ecef) 100%) !important;
        border-right: 1px solid var(--border-primary, #e0e0e0) !important;
        overflow-y: auto !important;
        flex-shrink: 0 !important;
    }

    .desktop-interface .main-content {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        min-width: 600px !important;
        background: var(--bg-primary, #ffffff) !important;
    }

    .desktop-interface .right-sidebar {
        width: 300px !important;
        min-width: 300px !important;
        max-width: 300px !important;
        display: flex !important;
        flex-direction: column !important;
        background: linear-gradient(180deg, var(--bg-secondary, #f8f9fa) 0%, var(--bg-tertiary, #e9ecef) 100%) !important;
        border-left: 1px solid var(--border-primary, #e0e0e0) !important;
        overflow-y: auto !important;
        flex-shrink: 0 !important;
    }

    /* Ensure desktop elements are visible */
    .desktop-interface .header,
    .desktop-interface .messages-container,
    .desktop-interface .input-area,
    .desktop-interface .org-chart,
    .desktop-interface .role-progression-container {
        display: block !important;
        visibility: visible !important;
    }

    /* Ensure mobile elements are hidden on desktop */
    .desktop-interface .mobile-header,
    .desktop-interface .mobile-sidebar-left,
    .desktop-interface .mobile-main-content,
    .desktop-interface .mobile-interface {
        display: none !important;
        visibility: hidden !important;
    }
}

@media (max-width: 992px) {
    /* Show mobile interface, hide desktop interface */
    .mobile-interface {
        display: block !important;
        position: relative;
        width: 100%;
        height: 100vh;
        overflow: hidden;
    }

    .desktop-interface {
        display: none !important;
    }

    /* Force hide desktop elements */
    .game-header,
    .hover-header,
    .desktop-only {
        display: none !important;
        visibility: hidden !important;
    }

    /* Reset body classes that might interfere */
    body.sidebar-visible,
    body.right-sidebar-visible {
        overflow: hidden !important;
    }

    /* Ensure mobile interface takes full space */
    body {
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
    }
}

@media (min-width: 993px) {
    /* Show desktop interface, hide mobile interface */
    .mobile-interface {
        display: none !important;
    }

    .desktop-interface {
        display: block !important;
    }
}

/* ===== MOBILE-FIRST BASE STYLES ===== */

/* Viewport and base mobile optimizations */
@media (max-width: 992px) {
    /* Reset and optimize for mobile */
    html {
        font-size: 16px;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        touch-action: manipulation;
        scroll-behavior: smooth;
    }

    body {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.5;
        background: var(--bg-color, #121212) !important;
        color: var(--text-color, #ffffff) !important;
    }

    /* Hide desktop header on mobile */
    .game-header {
        display: none !important;
    }

    /* Remove desktop padding */
    .app-container {
        padding-top: 0 !important;
        margin: 0 !important;
        height: 100vh !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
    }
}

/* ===== MOBILE HEADER ===== */
@media (max-width: 992px) {
    /* Mobile header container */
    .mobile-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 56px;
        background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--primary-dark, #5a67d8) 100%);
        color: white;
        z-index: 1050;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
    }

    /* Mobile header title */
    .mobile-header-title {
        font-size: 18px;
        font-weight: 600;
        flex: 1;
        text-align: center;
        margin: 0 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Mobile header buttons */
    .mobile-header-btn {
        width: 44px;
        height: 44px;
        border: none;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-header-btn:hover,
    .mobile-header-btn:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.95);
    }

    .mobile-header-btn.active {
        background: rgba(255, 255, 255, 0.25);
    }

    .mobile-header-spacer {
        width: 44px; /* Same width as button for balance */
        height: 44px;
    }

    /* Company badge in mobile header */
    .mobile-company-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
}

/* ===== MOBILE LAYOUT CONTAINER ===== */
@media (max-width: 992px) {
    .mobile-game-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        padding-top: 56px; /* Header height */
        background: var(--bg-color, #121212);
    }

    /* Left sidebar - collapsible accordion with scrolling */
    .mobile-sidebar-left {
        width: 100%;
        background: linear-gradient(180deg, var(--sidebar-bg, #1e1e1e) 0%, var(--bg-color, #121212) 100%);
        border-bottom: 1px solid var(--border-color, #444444);
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        order: 1;
        position: relative;
    }

    .mobile-sidebar-left.collapsed {
        height: 0;
        min-height: 0;
        max-height: 0;
        border-bottom: none;
    }

    .mobile-sidebar-left.expanded {
        height: auto;
        max-height: 60vh; /* 60% of viewport height for scrolling */
        min-height: 200px;
    }

    /* Right sidebar - collapsible accordion with scrolling */
    .mobile-sidebar-right {
        width: 100%;
        background: linear-gradient(180deg, var(--sidebar-bg, #1e1e1e) 0%, var(--bg-color, #121212) 100%);
        border-bottom: 1px solid var(--border-color, #444444);
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        order: 3;
        position: relative;
    }

    .mobile-sidebar-right.collapsed {
        height: 0;
        min-height: 0;
        max-height: 0;
        border-bottom: none;
    }

    .mobile-sidebar-right.expanded {
        height: auto;
        max-height: 60vh; /* 60% of viewport height for scrolling */
        min-height: 200px;
    }

    /* Sidebar content container with scrolling */
    .mobile-sidebar-content {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px 16px;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color, #667eea) transparent;
    }

    /* Custom scrollbar for webkit browsers */
    .mobile-sidebar-content::-webkit-scrollbar {
        width: 4px;
    }

    .mobile-sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-sidebar-content::-webkit-scrollbar-thumb {
        background: var(--primary-color, #667eea);
        border-radius: 2px;
    }

    .mobile-sidebar-content::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark, #5a67d8);
    }

    /* Main content area */
    .mobile-main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        order: 2;
        background: var(--main-bg, #ffffff);
        color: var(--text-color, #333333);
    }

    /* Messages container */
    .mobile-messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* Input area */
    .mobile-input-area {
        padding: 16px;
        background: var(--input-bg, #f8f9fa);
        border-top: 1px solid var(--border-color, #e0e0e0);
        min-height: 120px;
    }
}

/* ===== MOBILE TYPOGRAPHY ===== */
@media (max-width: 992px) {
    .mobile-sidebar-left h1,
    .mobile-sidebar-right h1 {
        font-size: 20px;
        font-weight: 700;
        margin: 0 0 16px 0;
        color: var(--text-color, #ffffff);
    }

    .mobile-sidebar-left h2,
    .mobile-sidebar-right h2 {
        font-size: 18px;
        font-weight: 600;
        margin: 16px 0 12px 0;
        color: var(--text-color, #ffffff);
    }

    .mobile-sidebar-left h3,
    .mobile-sidebar-right h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 12px 0 8px 0;
        color: var(--text-color, #ffffff);
    }

    .mobile-sidebar-left p,
    .mobile-sidebar-right p {
        font-size: 14px;
        line-height: 1.5;
        margin: 0 0 12px 0;
        color: var(--text-color, #ffffff);
    }

    .mobile-sidebar-left .stat-label,
    .mobile-sidebar-right .stat-label {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-muted, #888888);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .mobile-sidebar-left .stat-value,
    .mobile-sidebar-right .stat-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color, #ffffff);
    }
}

/* ===== MOBILE BUTTONS ===== */
@media (max-width: 992px) {
    .mobile-btn {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .mobile-btn-primary {
        background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--primary-dark, #5a67d8) 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .mobile-btn-secondary {
        background: var(--button-secondary-bg, #f0f0f0);
        color: var(--button-secondary-text, #333);
        border: 1px solid var(--border-color, #e0e0e0);
    }

    .mobile-btn-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .mobile-btn-success:hover {
        background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
    }

    .mobile-btn-success:active {
        background: linear-gradient(135deg, #2d7bfe 0%, #00c4fe 100%);
        transform: scale(0.95);
    }

    .mobile-btn:active {
        transform: scale(0.95);
    }

    .mobile-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
}

/* ===== MOBILE INPUT ELEMENTS ===== */
@media (max-width: 992px) {
    .mobile-input {
        width: 100%;
        min-height: 44px;
        padding: 12px 16px;
        border: 2px solid var(--input-border, #e0e0e0);
        border-radius: 8px;
        font-size: 16px; /* Prevent zoom on iOS */
        background: var(--input-bg, #ffffff);
        color: var(--text-color, #333333);
        transition: border-color 0.2s ease;
        -webkit-appearance: none;
        resize: vertical;
    }

    .mobile-input:focus {
        outline: none;
        border-color: var(--primary-color, #667eea);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .mobile-textarea {
        min-height: 80px;
        max-height: 200px;
        font-family: inherit;
        line-height: 1.5;
    }
}

/* ===== MOBILE CARDS AND CONTAINERS ===== */
@media (max-width: 992px) {
    .mobile-card {
        background: var(--card-bg, #ffffff);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--border-color, #e0e0e0);
    }

    .mobile-stat-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin: 16px 0;
    }

    .mobile-stat-item {
        text-align: center;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid var(--border-color, #444444);
    }

    /* Context items */
    .mobile-context-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-context-item:last-child {
        border-bottom: none;
    }

    /* Instructions list */
    .mobile-instructions {
        padding-left: 20px;
        margin: 12px 0;
        color: var(--text-color, #ffffff);
    }

    .mobile-instructions li {
        margin: 8px 0;
        line-height: 1.4;
    }

    .mobile-tip {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid var(--primary-color, #667eea);
        border-radius: 8px;
        padding: 12px;
        margin: 12px 0;
        font-size: 14px;
        color: var(--primary-color, #667eea);
        font-weight: 500;
    }

    /* Game progress */
    .mobile-game-progress {
        margin-top: 16px;
        padding: 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid var(--border-color, #444444);
    }

    .mobile-progress-item {
        display: flex;
        justify-content: space-between;
        margin: 8px 0;
        font-size: 14px;
    }

    .mobile-completion-badge {
        background: var(--success-color, #4facfe);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        text-align: center;
        margin-top: 12px;
        font-weight: 500;
    }

    /* Shortcuts */
    .mobile-shortcuts {
        margin: 12px 0;
    }

    .mobile-shortcut-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-shortcut-item:last-child {
        border-bottom: none;
    }

    .mobile-shortcut-key {
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        font-weight: 500;
    }

    .mobile-shortcut-desc {
        font-size: 13px;
        color: var(--text-muted, #888888);
    }

    /* Organization Chart */
    .mobile-org-chart {
        margin: 16px 0;
    }

    .mobile-org-level {
        margin: 12px 0;
        position: relative;
    }

    .mobile-org-level.current {
        background: rgba(102, 126, 234, 0.1);
        border-radius: 8px;
        padding: 8px;
        border: 2px solid var(--primary-color, #667eea);
    }

    .mobile-org-role {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid var(--border-color, #444444);
        position: relative;
    }

    .mobile-org-role i {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .mobile-org-role.executive i { color: #ffd700; }
    .mobile-org-role.senior-manager i { color: #ff6b6b; }
    .mobile-org-role.manager i { color: #4ecdc4; }
    .mobile-org-role.senior-employee i { color: #45b7d1; }
    .mobile-org-role.employee i { color: #96ceb4; }
    .mobile-org-role.applicant i { color: var(--primary-color, #667eea); }

    .role-title {
        font-weight: 600;
        font-size: 12px;
        text-align: center;
        margin-bottom: 4px;
    }

    .role-desc {
        font-size: 11px;
        color: var(--text-muted, #888888);
        text-align: center;
    }

    .current-indicator {
        position: absolute;
        right: -10px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--primary-color, #667eea);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 500;
        white-space: nowrap;
    }

    /* Career Path */
    .mobile-career-path {
        margin: 16px 0;
    }

    .mobile-career-step {
        display: flex;
        align-items: flex-start;
        margin: 16px 0;
        position: relative;
    }

    .mobile-career-step:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 19px;
        top: 40px;
        width: 2px;
        height: 20px;
        background: var(--border-color, #444444);
    }

    .mobile-career-step.completed::after {
        background: var(--success-color, #4facfe);
    }

    .mobile-career-step.current::after {
        background: var(--primary-color, #667eea);
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--border-color, #444444);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .mobile-career-step.completed .step-number {
        background: var(--success-color, #4facfe);
    }

    .mobile-career-step.current .step-number {
        background: var(--primary-color, #667eea);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .step-content {
        flex: 1;
    }

    .step-content h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color, #ffffff);
    }

    .step-content p {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: var(--text-muted, #888888);
        line-height: 1.4;
    }

    .step-status {
        font-size: 11px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .step-status i {
        font-size: 12px;
    }

    .step-status.current {
        color: var(--primary-color, #667eea);
    }

    /* Requirements */
    .mobile-requirements {
        margin: 16px 0;
    }

    .requirement-category {
        margin: 16px 0;
    }

    .requirement-category h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--text-color, #ffffff);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .requirement-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .requirement-list li {
        padding: 6px 0;
        font-size: 13px;
        color: var(--text-color, #ffffff);
        position: relative;
        padding-left: 20px;
    }

    .requirement-list li::before {
        content: '•';
        color: var(--primary-color, #667eea);
        position: absolute;
        left: 0;
        font-weight: bold;
    }

    /* Company Info */
    .mobile-company-info {
        margin: 16px 0;
    }

    .company-description {
        font-size: 14px;
        line-height: 1.5;
        margin: 0 0 16px 0;
        color: var(--text-color, #ffffff);
    }

    .company-stats {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin: 16px 0;
    }

    .company-stat {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
    }

    .company-stat i {
        color: var(--primary-color, #667eea);
        width: 16px;
        text-align: center;
    }

    .company-stat .stat-label {
        font-size: 12px;
        color: var(--text-muted, #888888);
        min-width: 60px;
    }

    .company-stat .stat-value {
        font-size: 13px;
        font-weight: 500;
        color: var(--text-color, #ffffff);
    }

    .company-values {
        margin: 16px 0;
    }

    .company-values h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--text-color, #ffffff);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .values-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .value-tag {
        background: var(--primary-color, #667eea);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
    }

    /* Pro Tips */
    .mobile-tips {
        margin: 16px 0;
    }

    .tip-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin: 12px 0;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border-left: 3px solid var(--primary-color, #667eea);
    }

    .tip-icon {
        color: var(--primary-color, #667eea);
        font-size: 16px;
        margin-top: 2px;
        flex-shrink: 0;
    }

    .tip-content {
        font-size: 13px;
        line-height: 1.4;
        color: var(--text-color, #ffffff);
    }

    .tip-content strong {
        color: var(--primary-color, #667eea);
    }

    /* Help section */
    .mobile-help {
        margin: 16px 0;
    }

    .mobile-help p {
        font-size: 14px;
        line-height: 1.5;
        margin: 0 0 16px 0;
        color: var(--text-color, #ffffff);
    }

    /* ===== MOBILE LOADING OVERLAY ===== */
    .mobile-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(4px);
    }

    .mobile-loading-content {
        background: var(--bg-color, #121212);
        border-radius: 16px;
        padding: 32px;
        text-align: center;
        border: 1px solid var(--border-color, #444444);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    }

    .mobile-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid var(--border-color, #444444);
        border-top: 3px solid var(--primary-color, #667eea);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mobile-loading-text {
        color: var(--text-color, #ffffff);
        font-size: 16px;
        font-weight: 500;
    }

    /* ===== MOBILE MESSAGES ===== */
    .mobile-message {
        margin: 12px 0;
        padding: 12px 16px;
        border-radius: 12px;
        max-width: 85%;
        word-wrap: break-word;
        animation: slideInMessage 0.3s ease-out;
    }

    @keyframes slideInMessage {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .mobile-message-user {
        background: var(--primary-color, #667eea);
        color: white;
        margin-left: auto;
        border-bottom-right-radius: 4px;
    }

    .mobile-message-ai {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-color, #ffffff);
        margin-right: auto;
        border: 1px solid var(--border-color, #444444);
        border-bottom-left-radius: 4px;
    }

    .mobile-message-system {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        margin: 0 auto;
        text-align: center;
        border: 1px solid rgba(255, 193, 7, 0.3);
        font-size: 14px;
    }

    .mobile-message-content {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .mobile-message-text {
        line-height: 1.4;
        font-size: 14px;
    }

    .mobile-message-time {
        font-size: 11px;
        opacity: 0.7;
        align-self: flex-end;
    }

    .mobile-message-ai .mobile-message-time {
        align-self: flex-start;
    }
}

/* ===== MOBILE MESSAGES ===== */
@media (max-width: 992px) {
    .mobile-message {
        margin-bottom: 16px;
        padding: 16px;
        border-radius: 12px;
        max-width: 100%;
        word-wrap: break-word;
        animation: slideInUp 0.3s ease-out;
    }

    .mobile-message.user {
        background: linear-gradient(135deg, var(--message-you-bg, #e3f2fd) 0%, rgba(227, 242, 253, 0.8) 100%);
        color: var(--text-color, #333);
        margin-left: 20px;
        border-bottom-right-radius: 4px;
    }

    .mobile-message.ai {
        background: linear-gradient(135deg, var(--ai-message-bg, #f0f7ea) 0%, rgba(240, 247, 234, 0.8) 100%);
        color: var(--text-color, #333);
        margin-right: 20px;
        border-bottom-left-radius: 4px;
        border-left: 3px solid var(--ai-message-border, #4caf50);
    }

    .mobile-message.system {
        background: var(--message-bg, #f0f2f5);
        color: var(--text-color, #333);
        text-align: center;
        font-style: italic;
        margin: 8px 0;
        padding: 12px;
    }

    .mobile-message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 12px;
        color: var(--text-muted, #666);
    }

    .mobile-message-time {
        font-size: 11px;
        opacity: 0.7;
    }

    .mobile-message-content {
        font-size: 15px;
        line-height: 1.5;
    }
}

/* ===== MOBILE ANIMATIONS ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 400px;
        opacity: 1;
    }
}

/* ===== MOBILE PROGRESS INDICATORS ===== */
@media (max-width: 992px) {
    .mobile-progress-container {
        width: 100%;
        height: 6px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;
        margin: 12px 0;
    }

    .mobile-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--success-color, #4facfe) 0%, var(--primary-color, #667eea) 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
        position: relative;
    }

    .mobile-progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
}

/* ===== MOBILE MODALS ===== */
@media (max-width: 992px) {
    .mobile-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1060;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        backdrop-filter: blur(5px);
    }

    .mobile-modal-content {
        background: var(--modal-bg, #ffffff);
        border-radius: 16px;
        padding: 24px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.9) translateY(20px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .mobile-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color, #e0e0e0);
    }

    .mobile-modal-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-color, #333);
        margin: 0;
    }

    .mobile-modal-close {
        width: 32px;
        height: 32px;
        border: none;
        background: none;
        font-size: 24px;
        color: var(--text-muted, #666);
        cursor: pointer;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
    }

    .mobile-modal-close:hover {
        background: var(--button-secondary-bg, #f0f0f0);
    }

    .mobile-modal-body {
        color: var(--text-color, #333);
    }

    .mobile-modal-body h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 16px 0;
        color: var(--text-color, #333);
    }

    .mobile-modal-body h4 {
        font-size: 16px;
        font-weight: 600;
        margin: 16px 0 8px 0;
        color: var(--text-color, #333);
    }

    .mobile-modal-body ol,
    .mobile-modal-body ul {
        padding-left: 20px;
        margin: 12px 0;
    }

    .mobile-modal-body li {
        margin: 8px 0;
        line-height: 1.5;
    }

    .mobile-modal-body p {
        margin: 12px 0;
        line-height: 1.5;
    }
}

/* ===== MOBILE FORM ELEMENTS ===== */
@media (max-width: 992px) {
    .mobile-form-group {
        margin-bottom: 20px;
    }

    .mobile-form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color, #333);
        margin-bottom: 8px;
    }

    .mobile-form-help {
        font-size: 12px;
        color: var(--text-muted, #666);
        margin-top: 4px;
        line-height: 1.4;
    }

    .mobile-button-group {
        display: flex;
        gap: 12px;
        margin-top: 20px;
    }

    .mobile-button-group .mobile-btn {
        flex: 1;
    }
}

/* ===== MOBILE ACCESSIBILITY ===== */
@media (max-width: 992px) {
    /* Focus indicators */
    .mobile-btn:focus,
    .mobile-input:focus,
    .mobile-header-btn:focus {
        outline: 2px solid var(--primary-color, #667eea);
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .mobile-btn,
        .mobile-input,
        .mobile-card {
            border-width: 2px;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .mobile-sidebar-left,
        .mobile-sidebar-right,
        .mobile-btn,
        .mobile-message {
            transition: none;
            animation: none;
        }
    }

    /* Dark mode adjustments */
    @media (prefers-color-scheme: dark) {
        .mobile-input {
            background: var(--input-bg, #2a2a2a);
            color: var(--text-color, #ffffff);
            border-color: var(--input-border, #555555);
        }

        .mobile-card {
            background: var(--card-bg, #1e1e1e);
            border-color: var(--border-color, #444444);
        }
    }
}

/* ===== MOBILE LANDSCAPE ORIENTATION ===== */
@media (max-width: 992px) and (orientation: landscape) {
    .mobile-header {
        height: 48px;
    }

    .mobile-game-container {
        padding-top: 48px;
    }

    .mobile-sidebar-left.expanded,
    .mobile-sidebar-right.expanded {
        max-height: 40vh; /* Reduced height for landscape */
    }

    .mobile-input-area {
        min-height: 80px;
        padding: 12px 16px;
    }

    /* Ensure sidebars are more compact in landscape */
    .mobile-sidebar-content {
        padding: 16px 12px;
    }

    .mobile-card {
        margin-bottom: 12px;
        padding: 12px;
    }

    .mobile-stat-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
}

/* ===== SMALL MOBILE DEVICES (320px-375px) ===== */
@media (max-width: 375px) {
    .mobile-header {
        padding: 0 12px;
    }

    .mobile-header-title {
        font-size: 16px;
        margin: 0 12px;
    }

    .mobile-sidebar-left.expanded,
    .mobile-sidebar-right.expanded {
        max-height: 55vh; /* Slightly larger for small screens */
    }

    .mobile-sidebar-content {
        padding: 16px 12px;
    }

    .mobile-messages-container {
        padding: 12px;
    }

    .mobile-input-area {
        padding: 12px;
    }

    .mobile-btn {
        padding: 10px 16px;
        font-size: 15px;
    }

    .mobile-stat-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .mobile-card {
        padding: 12px;
        margin-bottom: 12px;
    }

    .mobile-org-role i {
        font-size: 20px;
    }

    .role-title {
        font-size: 11px;
    }

    .role-desc {
        font-size: 10px;
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .tip-content {
        font-size: 12px;
    }
}

/* ===== MOBILE TOAST NOTIFICATIONS ===== */
@media (max-width: 992px) {
    .mobile-toast {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%) translateY(100px);
        background: var(--card-bg, #ffffff);
        color: var(--text-color, #333333);
        padding: 16px 24px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        z-index: 1070;
        max-width: calc(100vw - 40px);
        font-size: 14px;
        font-weight: 500;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid var(--border-color, #e0e0e0);
    }

    .mobile-toast.show {
        transform: translateX(-50%) translateY(0);
    }

    .mobile-toast.mobile-toast-success {
        background: var(--success-color, #4facfe);
        color: white;
        border-color: var(--success-color, #4facfe);
    }

    .mobile-toast.mobile-toast-error {
        background: var(--danger-color, #ff9a9e);
        color: white;
        border-color: var(--danger-color, #ff9a9e);
    }

    .mobile-toast.mobile-toast-warning {
        background: var(--warning-color, #fcb69f);
        color: white;
        border-color: var(--warning-color, #fcb69f);
    }
}

/* ===== MOBILE LOADING STATES ===== */
@media (max-width: 992px) {
    .mobile-loading {
        position: relative;
        overflow: hidden;
    }

    .mobile-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
        animation: mobileShimmer 1.5s infinite;
    }

    @keyframes mobileShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .mobile-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: mobileSpinner 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes mobileSpinner {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
}

/* ===== MOBILE PULL-TO-REFRESH ===== */
@media (max-width: 992px) {
    .mobile-pull-to-refresh {
        position: absolute;
        top: -60px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        background: var(--primary-color, #667eea);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        transition: all 0.3s ease;
        opacity: 0;
    }

    .mobile-pull-to-refresh.visible {
        opacity: 1;
        top: 10px;
    }

    .mobile-pull-to-refresh.loading {
        animation: mobileSpinner 1s linear infinite;
    }
}

/* ===== MOBILE SWIPE INDICATORS ===== */
@media (max-width: 992px) {
    .mobile-swipe-hint {
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        z-index: 1060;
        animation: fadeInOut 3s ease-in-out;
        pointer-events: none;
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        20%, 80% { opacity: 1; }
    }
}

/* ===== MOBILE SAFE AREAS (iPhone X+) ===== */
@media (max-width: 992px) {
    .mobile-header {
        padding-top: env(safe-area-inset-top);
        height: calc(56px + env(safe-area-inset-top));
    }

    .mobile-game-container {
        padding-top: calc(56px + env(safe-area-inset-top));
        padding-bottom: env(safe-area-inset-bottom);
    }

    .mobile-input-area {
        padding-bottom: calc(16px + env(safe-area-inset-bottom));
    }

    .mobile-toast {
        bottom: calc(20px + env(safe-area-inset-bottom));
    }
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */
@media (max-width: 992px) {
    /* GPU acceleration for smooth animations */
    .mobile-sidebar-left,
    .mobile-sidebar-right,
    .mobile-header-btn,
    .mobile-btn,
    .mobile-message {
        will-change: transform;
        transform: translateZ(0);
    }

    /* Optimize scrolling performance */
    .mobile-messages-container {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        overscroll-behavior: contain;
    }

    /* Reduce repaints */
    .mobile-header {
        contain: layout style paint;
    }

    .mobile-sidebar-left,
    .mobile-sidebar-right {
        contain: layout style;
    }
}

/* ===== MOBILE DARK MODE ENHANCEMENTS ===== */
@media (max-width: 992px) {
    @media (prefers-color-scheme: dark) {
        .mobile-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        }

        .mobile-sidebar-left,
        .mobile-sidebar-right {
            background: linear-gradient(180deg, #1e1e1e 0%, #121212 100%);
        }

        .mobile-main-content {
            background: #0f0f0f;
            color: #ffffff;
        }

        .mobile-input-area {
            background: #1a1a1a;
            border-top-color: #333333;
        }

        .mobile-input {
            background: #2a2a2a;
            color: #ffffff;
            border-color: #555555;
        }

        .mobile-card {
            background: #1e1e1e;
            border-color: #444444;
        }

        .mobile-toast {
            background: #2a2a2a;
            color: #ffffff;
            border-color: #555555;
        }
    }
}

/* ===== MOBILE PRINT STYLES ===== */
@media print and (max-width: 992px) {
    .mobile-header,
    .mobile-sidebar-left,
    .mobile-sidebar-right,
    .mobile-input-area {
        display: none !important;
    }

    .mobile-main-content {
        padding: 0 !important;
        margin: 0 !important;
    }

    .mobile-message {
        break-inside: avoid;
        margin-bottom: 10px;
    }
}
