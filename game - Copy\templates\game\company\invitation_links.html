{% extends 'game/company/base.html' %}

{% block title %}Invitation Links - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Invitation Links</h1>
        <a href="{% url 'game:company_team' company_slug=company.slug %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> Back to Team
        </a>
    </div>
    
    <!-- Invitation Links -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Share These Links</h5>
        </div>
        <div class="card-body">
            <p class="text-muted mb-4">
                The following links have been generated for your invitations. Share these links with your team members to join your company.
            </p>
            
            <div class="list-group">
                {% for invitation in invitation_links %}
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ invitation.email }}</h6>
                            <code class="d-block mb-0">{{ request.scheme }}://{{ request.get_host }}{{ invitation.link }}</code>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary copy-link" data-link="{{ request.scheme }}://{{ request.get_host }}{{ invitation.link }}">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="alert alert-info mt-4">
                <i class="bi bi-info-circle-fill"></i> These links will expire in 7 days.
            </div>
            
            <div class="text-center mt-4">
                <a href="{% url 'game:company_team' company_slug=company.slug %}" class="btn btn-primary">
                    Return to Team Management
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy link functionality
        const copyButtons = document.querySelectorAll('.copy-link');
        copyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const link = this.getAttribute('data-link');
                navigator.clipboard.writeText(link).then(function() {
                    // Change button text temporarily
                    const originalHTML = button.innerHTML;
                    button.innerHTML = '<i class="bi bi-check"></i> Copied!';
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-success');
                    
                    // Revert after 2 seconds
                    setTimeout(function() {
                        button.innerHTML = originalHTML;
                        button.classList.remove('btn-success');
                        button.classList.add('btn-outline-primary');
                    }, 2000);
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    alert('Failed to copy link. Please try again.');
                });
            });
        });
    });
</script>
{% endblock %}
