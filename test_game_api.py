#!/usr/bin/env python3
"""
Test script to verify the game API is working correctly with the new Gemini API key.
"""

import requests
import json
import sys

def test_game_api():
    """Test the game API endpoints"""
    base_url = "http://localhost:8000/game/api"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("Testing Game API...")
    print("=" * 50)
    
    # Test 1: Get game state
    print("1. Testing get_game_state endpoint...")
    try:
        response = session.get(f"{base_url}/get_game_state/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Game state retrieved successfully")
            print(f"   Current role: {data.get('current_role', 'Unknown')}")
            print(f"   Performance score: {data.get('performance_score', 'Unknown')}")
            print(f"   Messages count: {len(data.get('messages', []))}")
        else:
            print(f"❌ Failed to get game state: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting game state: {e}")
        return False
    
    # Test 2: Start game
    print("\n2. Testing start_game endpoint...")
    try:
        # First get CSRF token
        csrf_response = session.get("http://localhost:8000/game/")
        csrf_token = None
        if 'csrftoken' in session.cookies:
            csrf_token = session.cookies['csrftoken']
        
        headers = {
            'X-CSRFToken': csrf_token,
            'Referer': 'http://localhost:8000/game/'
        }
        
        response = session.get(f"{base_url}/start_game/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Game started successfully")
            print(f"   Status: {data.get('status', 'Unknown')}")
        else:
            print(f"❌ Failed to start game: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error starting game: {e}")
        return False
    
    # Test 3: Test LLM integration with a simple prompt
    print("\n3. Testing LLM integration with submit_prompt...")
    try:
        prompt_data = {
            'prompt': 'Dear Hiring Manager, I am writing to express my interest in the position at your company. I have relevant experience and would be a great fit for this role. Thank you for your consideration.'
        }
        
        response = session.post(f"{base_url}/submit_prompt/", 
                              data=json.dumps(prompt_data),
                              headers={
                                  'Content-Type': 'application/json',
                                  'X-CSRFToken': csrf_token,
                                  'Referer': 'http://localhost:8000/game/'
                              })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Prompt submitted successfully")
            print(f"   Status: {data.get('status', 'Unknown')}")
            print(f"   Score: {data.get('overall_score', 'Unknown')}")
            print(f"   Meets requirements: {data.get('meets_requirements', 'Unknown')}")
            
            # Check if LLM response was generated
            if 'ai_response' in data:
                print(f"   AI Response generated: {len(data['ai_response'])} characters")
                print(f"✅ LLM integration working correctly!")
            else:
                print(f"⚠️  No AI response in data - LLM might be offline")
                
        else:
            print(f"❌ Failed to submit prompt: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error submitting prompt: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ All tests completed successfully!")
    print("🎉 Game replacement was successful!")
    return True

if __name__ == "__main__":
    success = test_game_api()
    sys.exit(0 if success else 1)
