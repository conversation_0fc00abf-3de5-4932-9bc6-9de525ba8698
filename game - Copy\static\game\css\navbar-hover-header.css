/* Navbar-style Hover Header */

.hover-header {
    position: fixed;
    top: -60px; /* Start off-screen */
    left: 0;
    right: 0;
    height: 56px; /* Match Bootstrap navbar height */
    background-color: #0d6efd; /* Bootstrap primary color */
    color: white;
    z-index: 9999;
    transition: top 0.3s ease-in-out;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.hover-header.visible {
    top: 0;
}

.hover-header .container {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    max-width: 1140px;
    margin: 0 auto;
}

.hover-header .navbar-brand {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 500;
    text-decoration: none;
    margin-right: 1rem;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
}

.hover-header .navbar-brand i {
    margin-right: 0.5rem;
}

.hover-header .navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Fix spacing between icon and text */
.hover-header .nav-link i,
.hover-header .navbar-brand i {
    margin-right: 0.5rem;
}

.hover-header .nav-item {
    margin-right: 0.5rem;
}

.hover-header .nav-link {
    color: rgba(255, 255, 255, 0.85);
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-size: 1rem;
    transition: color 0.15s ease-in-out;
}

.hover-header .nav-link:hover {
    color: white;
}

.hover-header .nav-link i {
    margin-right: 0.25rem;
}

.hover-header .navbar-nav.ms-auto {
    margin-left: auto;
}

.hover-header .btn-link {
    color: rgba(255, 255, 255, 0.85);
    background: none;
    border: none;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.hover-header .btn-link:hover {
    color: white;
}

/* Dark mode adjustments */
html.dark-mode .hover-header,
body.dark-mode .hover-header {
    background-color: #0d47a1; /* Darker blue in dark mode */
}

/* Animation for the header when it appears */
@keyframes headerFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hover-header.visible {
    animation: headerFadeIn 0.3s ease-out;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .hover-header .container {
        padding: 0 10px;
    }

    .hover-header .navbar-brand {
        font-size: 1.1rem;
    }

    .hover-header .nav-link {
        padding: 0.5rem 0.5rem;
        font-size: 0.9rem;
    }
}
