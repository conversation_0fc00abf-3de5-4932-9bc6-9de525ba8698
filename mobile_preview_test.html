<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Preview Test - Corporate Prompt Master</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border-left: 4px solid #28a745;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #5a67d8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin: 8px 8px 8px 0;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .mobile-demo {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            margin: 16px 0;
            overflow: hidden;
            background: white;
            max-width: 375px;
            margin: 16px auto;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #5a67d8 100%);
            color: white;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
        }
        
        .demo-content {
            padding: 16px;
        }
        
        .demo-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            resize: vertical;
            min-height: 60px;
        }
        
        .demo-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            margin-right: 8px;
            margin-bottom: 8px;
            cursor: pointer;
        }
        
        .demo-preview {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            font-size: 13px;
            line-height: 1.4;
            min-height: 80px;
            display: none;
        }
        
        .demo-preview.visible {
            display: block;
        }
        
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 8px 0;
            overflow-x: auto;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            background: #28a745;
            color: white;
        }
        
        .device-info {
            background: #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 Mobile Preview Test</h1>
        <p>This page tests that the AI response preview now works correctly in mobile mode.</p>
        
        <div class="status-card success">
            <h2>✅ Mobile Preview Functionality Added</h2>
            <p><strong>Problem:</strong> AI response preview was not showing in mobile mode.</p>
            <p><strong>Solution:</strong> Added dedicated mobile preview container with desktop integration.</p>
        </div>
        
        <div class="status-card">
            <h2>📏 Device Detection</h2>
            <p>Current device information:</p>
            <div class="device-info">
                <div>Screen Width: <span id="screen-width">Loading...</span>px</div>
                <div>Viewport Width: <span id="viewport-width">Loading...</span>px</div>
                <div>User Agent: <span id="user-agent">Loading...</span></div>
                <div>Expected Mode: <span id="expected-mode">Loading...</span></div>
                <div>Touch Support: <span id="touch-support">Loading...</span></div>
            </div>
        </div>
        
        <div class="status-card">
            <h2>🔧 Mobile Preview Implementation</h2>
            <p>The mobile preview system includes:</p>
            
            <ul class="feature-list">
                <li>
                    <div class="check-icon">✓</div>
                    <span>Dedicated mobile preview container in HTML</span>
                </li>
                <li>
                    <div class="check-icon">✓</div>
                    <span>Mobile-optimized CSS styles for preview display</span>
                </li>
                <li>
                    <div class="check-icon">✓</div>
                    <span>JavaScript integration with desktop preview logic</span>
                </li>
                <li>
                    <div class="check-icon">✓</div>
                    <span>Real-time monitoring of desktop preview responses</span>
                </li>
                <li>
                    <div class="check-icon">✓</div>
                    <span>Mobile-friendly preview controls (close, edit, submit)</span>
                </li>
                <li>
                    <div class="check-icon">✓</div>
                    <span>Smooth animations and touch-optimized interactions</span>
                </li>
            </ul>
        </div>
        
        <div class="status-card">
            <h2>📱 Mobile Preview Demo</h2>
            <p>Here's how the mobile preview should look and work:</p>
            
            <div class="mobile-demo">
                <div class="demo-header">
                    <i class="fas fa-mobile-alt"></i> Corporate Prompt Master
                </div>
                <div class="demo-content">
                    <label for="demo-prompt">Your Prompt:</label>
                    <textarea id="demo-prompt" class="demo-input" placeholder="Enter your prompt here...">Write a professional email to request a meeting with the marketing team.</textarea>
                    
                    <button class="demo-button" onclick="showDemoPreview()">
                        👁️ Preview
                    </button>
                    <button class="demo-button" style="background: #6c757d;">
                        🔄 Restart
                    </button>
                    
                    <div id="demo-preview" class="demo-preview">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #5a67d8 100%); color: white; padding: 12px; margin: -12px -12px 12px -12px; border-radius: 8px 8px 0 0;">
                            <strong>👁️ AI Response Preview</strong>
                            <button style="float: right; background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 4px; padding: 4px 8px; cursor: pointer;" onclick="hideDemoPreview()">✕</button>
                        </div>
                        <div style="margin-bottom: 12px;">
                            Subject: Request for Marketing Team Meeting<br><br>
                            Dear Marketing Team,<br><br>
                            I hope this email finds you well. I would like to schedule a meeting to discuss our upcoming campaign strategies and align our objectives for the next quarter.<br><br>
                            Please let me know your availability for next week.<br><br>
                            Best regards,<br>
                            [Your Name]
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="demo-button" style="flex: 1; font-size: 12px;" onclick="hideDemoPreview()">✏️ Edit</button>
                            <button class="demo-button" style="flex: 1; font-size: 12px; background: #28a745;" onclick="hideDemoPreview()">📤 Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status-card">
            <h2>🔄 How Mobile Preview Works</h2>
            <div class="code-snippet">
1. User enters prompt in mobile interface
2. Mobile JavaScript syncs prompt with desktop input
3. Desktop preview function is triggered
4. Mobile monitors desktop preview container for changes
5. When desktop preview appears, mobile displays it
6. User can edit prompt or submit response from mobile preview
            </div>
        </div>
        
        <div class="status-card">
            <h2>🧪 Testing Instructions</h2>
            <ol>
                <li><strong>Open Game on Mobile:</strong> Navigate to <code>/game/</code> on mobile device or browser dev tools mobile view</li>
                <li><strong>Enter Prompt:</strong> Type a prompt in the mobile input area</li>
                <li><strong>Click Preview:</strong> Tap the "Preview" button</li>
                <li><strong>Verify Preview:</strong> Check that AI response appears in mobile preview container</li>
                <li><strong>Test Controls:</strong> Try "Edit Prompt" and "Submit Response" buttons</li>
                <li><strong>Test Close:</strong> Tap the X button to close preview</li>
            </ol>
            
            <button class="test-button" onclick="window.open('/game/', '_blank')">🎮 Open Game for Testing</button>
            <button class="test-button" onclick="runMobileTest()">📱 Run Mobile Test</button>
        </div>
        
        <div id="test-results" class="status-card" style="display: none;">
            <h2>📊 Test Results</h2>
            <div id="test-output">Test results will appear here...</div>
        </div>
        
        <div class="status-card">
            <h2>🎯 Expected Mobile Preview Features</h2>
            <ul style="color: #155724;">
                <li><strong>✅ Preview Display:</strong> AI response appears in mobile-optimized container</li>
                <li><strong>✅ Touch Controls:</strong> Large, touch-friendly buttons (44px minimum)</li>
                <li><strong>✅ Smooth Animation:</strong> Preview slides in smoothly</li>
                <li><strong>✅ Responsive Design:</strong> Adapts to different mobile screen sizes</li>
                <li><strong>✅ Desktop Integration:</strong> Uses same preview logic as desktop</li>
                <li><strong>✅ Error Handling:</strong> Shows loading states and error messages</li>
            </ul>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            document.getElementById('screen-width').textContent = screen.width;
            document.getElementById('viewport-width').textContent = window.innerWidth;
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
            document.getElementById('expected-mode').textContent = window.innerWidth <= 992 ? 'Mobile' : 'Desktop';
            document.getElementById('touch-support').textContent = 'ontouchstart' in window ? 'Yes' : 'No';
        }
        
        function showDemoPreview() {
            const preview = document.getElementById('demo-preview');
            preview.classList.add('visible');
        }
        
        function hideDemoPreview() {
            const preview = document.getElementById('demo-preview');
            preview.classList.remove('visible');
        }
        
        function runMobileTest() {
            const results = document.getElementById('test-results');
            const output = document.getElementById('test-output');
            
            results.style.display = 'block';
            output.innerHTML = '🔄 Running mobile preview tests...';
            
            setTimeout(() => {
                const isMobile = window.innerWidth <= 992;
                const hasTouch = 'ontouchstart' in window;
                
                let testResults = [];
                testResults.push(`<strong>Mobile Preview Test Results:</strong>`);
                testResults.push(`Device Mode: ${isMobile ? 'Mobile' : 'Desktop'}`);
                testResults.push(`Touch Support: ${hasTouch ? 'Yes' : 'No'}`);
                testResults.push(`Viewport: ${window.innerWidth}px`);
                testResults.push('');
                
                if (isMobile) {
                    testResults.push('✅ Mobile mode detected');
                    testResults.push('✅ Mobile preview container should be available');
                    testResults.push('✅ Touch-optimized controls should be active');
                    testResults.push('✅ Preview monitoring should work');
                } else {
                    testResults.push('🖥️ Desktop mode detected');
                    testResults.push('🖥️ Desktop preview should work normally');
                    testResults.push('📱 Switch to mobile view to test mobile preview');
                }
                
                testResults.push('');
                testResults.push('<strong>Next Steps:</strong>');
                testResults.push('1. Open /game/ in mobile view');
                testResults.push('2. Enter a prompt and click Preview');
                testResults.push('3. Verify mobile preview container appears');
                testResults.push('4. Test edit and submit buttons');
                
                output.innerHTML = testResults.join('<br>');
            }, 1500);
        }
        
        // Auto-update device info
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('load', () => {
            updateDeviceInfo();
            console.log('Mobile Preview Test Loaded');
            console.log('Current mode:', window.innerWidth <= 992 ? 'Mobile' : 'Desktop');
        });
        
        // Initial update
        updateDeviceInfo();
    </script>
</body>
</html>
