import logging
import random
from datetime import timedelta
from django.utils import timezone
from django.core.management import call_command
from .models import AnonymousPlayerSettings

class AnonymousPlayerCleanupMiddleware:
    """
    Middleware to periodically clean up anonymous player data.
    This runs the cleanup command with a small probability on each request
    to avoid performance impact while ensuring regular cleanup.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Process the request first
        response = self.get_response(request)
        
        # Only run cleanup check with a small probability (1%)
        # This prevents running the check on every request
        if random.random() < 0.01:
            self._check_cleanup_needed()
            
        return response
    
    def _check_cleanup_needed(self):
        """Check if cleanup is needed and run it if necessary"""
        try:
            # Get settings
            settings = AnonymousPlayerSettings.objects.first()
            
            # If no settings exist, create default settings
            if not settings:
                settings = AnonymousPlayerSettings.objects.create(
                    cleanup_enabled=True,
                    cleanup_hours=24
                )
                logging.info("Created default anonymous player cleanup settings")
                return
            
            # Skip if cleanup is disabled
            if not settings.cleanup_enabled:
                return
            
            # Check if it's time to run cleanup
            last_cleanup = settings.last_cleanup
            cleanup_interval = timedelta(hours=settings.cleanup_hours)
            
            # If never run or it's been longer than the cleanup interval
            if not last_cleanup or timezone.now() - last_cleanup > cleanup_interval:
                logging.info("Running anonymous player cleanup")
                
                # Run the cleanup command
                call_command('cleanup_anonymous_players')
                
        except Exception as e:
            # Log error but don't crash
            logging.error(f"Error in anonymous player cleanup middleware: {str(e)}")
