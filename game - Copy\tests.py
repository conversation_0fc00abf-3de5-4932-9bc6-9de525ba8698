"""
Tests for the Context-Aware Game Django application.
"""

import json
from django.test import Test<PERSON><PERSON>, Client
from django.urls import reverse
from .models import GameSession, Message
from .generate_org_chart import generate_org_chart_html


class GameViewsTestCase(TestCase):
    """Test case for game views."""

    def setUp(self):
        """Set up test environment."""
        self.client = Client()
        # Create a test session
        self.session = self.client.session
        self.session['session_id'] = 'test_session'
        self.session.save()

    def test_index_view(self):
        """Test that the index view returns a 200 status code"""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'game/index.html')

    def test_start_game_api(self):
        """Test that the start_game API endpoint returns a 200 status code"""
        response = self.client.get(reverse('start_game'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check that a game session was created
        self.assertTrue(GameSession.objects.filter(session_id='test_session').exists())

        # Check that the initial message was created
        game_session = GameSession.objects.get(session_id='test_session')
        self.assertTrue(Message.objects.filter(session=game_session).exists())

    def test_get_game_state_api(self):
        """Test that the get_game_state API endpoint returns a 200 status code"""
        # Create a game session first
        game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Create a message
        Message.objects.create(
            session=game_session,
            sender='hr',
            text='Welcome to the game!',
            html='<p>Welcome to the game!</p>'
        )

        response = self.client.get(reverse('get_game_state'))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['current_role'], 'applicant')
        self.assertEqual(data['current_task'], 'cover_letter')
        self.assertEqual(data['current_manager'], 'hr')
        self.assertEqual(len(data['messages']), 1)

        # Check that role progression HTML is present
        self.assertIn('role_progression_html', data)
        self.assertIsNotNone(data['role_progression_html'])

        # Check that org chart HTML is present
        self.assertIn('org_chart_html', data)
        self.assertIsNotNone(data['org_chart_html'])

    def test_preview_response_api(self):
        """Test that the preview_response API endpoint returns a 200 status code"""
        # Create a game session first
        GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Test with POST request (new Django API)
        response = self.client.post(
            reverse('preview_response'),
            json.dumps({
                'prompt': 'Test prompt',
                'task_id': 'cover_letter'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('ai_response', data)
        self.assertIn('html_response', data)
        self.assertIn('prompt_evaluation_grade', data)

        # Test with GET request (legacy Flask API)
        response = self.client.get(
            reverse('preview_response_legacy'),
            {'prompt': 'Test prompt', 'task_id': 'cover_letter'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

    def test_fetch_first_task_api(self):
        """Test that the fetch_first_task API endpoint returns a 200 status code"""
        # Create a game session first
        GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task=None,
            current_manager=None
        )

        response = self.client.get(reverse('fetch_first_task'))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['task_id'], 'cover_letter')
        self.assertEqual(data['manager'], 'hr')
        self.assertIn('description', data)
        self.assertIn('response_template', data)

        # Check that the game session was updated
        game_session = GameSession.objects.get(session_id='test_session')
        self.assertEqual(game_session.current_task, 'cover_letter')
        self.assertEqual(game_session.current_manager, 'hr')

    def test_submit_prompt_api(self):
        """Test that the submit_prompt API endpoint returns a 200 status code"""
        # Create a game session first
        game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Test with POST request (new Django API)
        response = self.client.post(
            reverse('submit_prompt'),
            json.dumps({
                'prompt': 'Test prompt',
                'task_id': 'cover_letter'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')

        # Check that a new message was created
        self.assertTrue(Message.objects.filter(session=game_session, sender='player').exists())

        # Check that the AI response message was created
        self.assertTrue(Message.objects.filter(session=game_session, sender='hr').exists())


class OrgChartTest(TestCase):
    """Test case for organization chart functionality."""

    def test_generate_org_chart_html(self):
        """Test the generate_org_chart_html function."""
        # Generate org chart HTML for the applicant role
        html = generate_org_chart_html('applicant', [])

        # Check that the HTML contains the expected elements
        self.assertIn('org-chart', html)
        self.assertIn('org-level', html)
        self.assertIn('org-node', html)
        self.assertIn('current', html)
        self.assertIn('locked', html)

        # Check that the applicant node is marked as current
        self.assertIn('data-position="applicant"', html)
