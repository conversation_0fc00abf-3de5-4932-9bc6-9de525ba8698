"""
Tests for the Context-Aware Game Django models.
"""

from django.test import TestCase
from .models import GameSession, Message


class GameSessionModelTest(TestCase):
    """Test case for GameSession model."""

    def setUp(self):
        """Set up test environment."""
        self.game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

    def test_game_session_creation(self):
        """Test that a GameSession can be created."""
        self.assertTrue(isinstance(self.game_session, GameSession))
        self.assertEqual(self.game_session.__str__(), 'test_session')

    def test_game_session_fields(self):
        """Test that GameSession fields are saved correctly."""
        self.assertEqual(self.game_session.session_id, 'test_session')
        self.assertEqual(self.game_session.current_role, 'applicant')
        self.assertEqual(self.game_session.performance_score, 0)
        self.assertEqual(self.game_session.challenges_completed, 0)
        self.assertEqual(self.game_session.role_challenges_completed, 0)
        self.assertEqual(self.game_session.current_task, 'cover_letter')
        self.assertEqual(self.game_session.current_manager, 'hr')

    def test_game_session_update(self):
        """Test that a GameSession can be updated."""
        self.game_session.current_role = 'junior_assistant'
        self.game_session.performance_score = 10
        self.game_session.challenges_completed = 1
        self.game_session.role_challenges_completed = 1
        self.game_session.current_task = 'meeting_summary'
        self.game_session.current_manager = 'manager'
        self.game_session.save()

        updated_session = GameSession.objects.get(session_id='test_session')
        self.assertEqual(updated_session.current_role, 'junior_assistant')
        self.assertEqual(updated_session.performance_score, 10)
        self.assertEqual(updated_session.challenges_completed, 1)
        self.assertEqual(updated_session.role_challenges_completed, 1)
        self.assertEqual(updated_session.current_task, 'meeting_summary')
        self.assertEqual(updated_session.current_manager, 'manager')


class MessageModelTest(TestCase):
    """Test case for Message model."""

    def setUp(self):
        """Set up test environment."""
        self.game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )
        self.message = Message.objects.create(
            session=self.game_session,
            sender='hr',
            text='Welcome to the game!',
            html='<p>Welcome to the game!</p>'
        )

    def test_message_creation(self):
        """Test that a Message can be created."""
        self.assertTrue(isinstance(self.message, Message))
        self.assertEqual(self.message.__str__(), 'Message from hr')

    def test_message_fields(self):
        """Test that Message fields are saved correctly."""
        self.assertEqual(self.message.session, self.game_session)
        self.assertEqual(self.message.sender, 'hr')
        self.assertEqual(self.message.text, 'Welcome to the game!')
        self.assertEqual(self.message.html, '<p>Welcome to the game!</p>')
        self.assertIsNotNone(self.message.timestamp)

    def test_message_ordering(self):
        """Test that messages are ordered by timestamp."""
        # Create a second message
        second_message = Message.objects.create(
            session=self.game_session,
            sender='player',
            text='Hello!',
            html='<p>Hello!</p>'
        )

        # Get all messages for the session
        messages = Message.objects.filter(session=self.game_session)
        
        # Check that the first message is the one we created first
        self.assertEqual(messages[0], self.message)
        self.assertEqual(messages[1], second_message)

    def test_message_relationship(self):
        """Test the relationship between GameSession and Message."""
        # Get all messages for the session
        messages = self.game_session.message_set.all()
        
        # Check that there is one message
        self.assertEqual(messages.count(), 1)
        
        # Check that the message is the one we created
        self.assertEqual(messages[0], self.message)
