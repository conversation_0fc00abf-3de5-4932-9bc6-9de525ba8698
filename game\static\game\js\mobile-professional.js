/**
 * Professional Mobile Interface JavaScript
 * Handles mobile-specific interactions and optimizations
 */

class MobileProfessionalInterface {
    constructor() {
        this.isMobile = window.innerWidth <= 992;
        this.isInitialized = false;
        this.sidebarStates = {
            left: false,
            right: false
        };
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.isScrolling = false;

        this.init();
    }

    init() {
        if (!this.isMobile) return;

        console.log('[Mobile Professional] Initializing mobile interface');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.initializeMobileInterface();
        this.setupEventListeners();
        this.optimizeForMobile();
        this.setupTouchGestures();
        this.setupKeyboardOptimizations();

        // Set up integration with existing game
        setTimeout(() => {
            this.syncWithDesktopGame();
        }, 1000); // Wait for other scripts to initialize

        this.isInitialized = true;

        console.log('[Mobile Professional] Mobile interface initialized');
    }

    initializeMobileInterface() {
        // Show mobile interface
        const mobileInterface = document.querySelector('.mobile-interface');
        if (mobileInterface) {
            mobileInterface.style.display = 'block';
        }

        // Set up mobile header button events
        this.setupMobileHeaderButtons();

        // Set up mobile input events
        this.setupMobileInputEvents();

        console.log('[Mobile Professional] Mobile interface structure initialized');
    }

    setupMobileHeaderButtons() {
        const leftToggle = document.getElementById('mobile-left-toggle');

        if (leftToggle) {
            leftToggle.addEventListener('click', () => this.toggleSidebar('left'));
        }
    }

    setupMobileInputEvents() {
        const previewBtn = document.getElementById('mobile-preview-btn');
        const submitBtn = document.getElementById('mobile-submit-btn');
        const restartBtn = document.getElementById('mobile-restart-btn');
        const continueBtn = document.getElementById('mobile-continue-game');
        const restartGameBtn = document.getElementById('mobile-restart-game');

        // Mobile preview container buttons
        const editPreviewBtn = document.getElementById('mobile-edit-preview-btn');
        const closePreviewBtn = document.getElementById('mobile-close-preview-btn');
        const submitPreviewBtn = document.getElementById('mobile-submit-preview-btn');

        // Mobile edit container buttons
        const backToPreviewBtn = document.getElementById('mobile-back-to-preview-btn');
        const submitEditedBtn = document.getElementById('mobile-submit-edited-btn');

        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.handlePreview());
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', () => this.handleSubmit());
        }

        if (restartBtn) {
            restartBtn.addEventListener('click', () => this.handleRestart());
        }

        if (continueBtn) {
            continueBtn.addEventListener('click', () => this.handleContinue());
        }

        if (restartGameBtn) {
            restartGameBtn.addEventListener('click', () => this.handleRestart());
        }

        // Mobile preview buttons
        if (editPreviewBtn) {
            editPreviewBtn.addEventListener('click', () => this.showMobileEdit());
        }

        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', () => this.hideMobilePreview());
        }

        if (submitPreviewBtn) {
            submitPreviewBtn.addEventListener('click', () => this.handleSubmitPreview());
        }

        // Mobile edit buttons
        if (backToPreviewBtn) {
            backToPreviewBtn.addEventListener('click', () => this.backToPreview());
        }

        if (submitEditedBtn) {
            submitEditedBtn.addEventListener('click', () => this.handleSubmitEdited());
        }
    }

    // Removed createMobileStructure - using HTML structure instead

    // Removed dynamic mobile structure creation methods - using HTML structure instead

    toggleSidebar(side) {
        const sidebar = document.getElementById(`mobile-sidebar-${side}`);
        if (!sidebar) return;

        const isExpanded = !sidebar.classList.contains('collapsed');

        if (isExpanded) {
            // Collapse
            sidebar.classList.add('collapsed');
            sidebar.classList.remove('expanded');
            this.sidebarStates[side] = false;
        } else {
            // Expand
            sidebar.classList.remove('collapsed');
            sidebar.classList.add('expanded');
            this.sidebarStates[side] = true;
        }

        // Update header button states
        this.updateHeaderButtonStates();

        // Announce to screen readers
        const action = isExpanded ? 'collapsed' : 'expanded';
        this.announceToScreenReader(`Game menu ${action}`);
    }

    updateHeaderButtonStates() {
        const leftBtn = document.getElementById('mobile-left-toggle');

        if (leftBtn) {
            leftBtn.classList.toggle('active', this.sidebarStates.left);
        }
    }

    setupEventListeners() {
        // Orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 300);
        });

        // Resize
        window.addEventListener('resize', () => this.handleResize());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Prevent zoom on double tap
        document.addEventListener('touchend', (e) => {
            if (e.target.closest('.mobile-input, .mobile-btn')) {
                e.preventDefault();
            }
        });
    }

    setupTouchGestures() {
        const mainContent = document.querySelector('.mobile-main-content');
        if (!mainContent) return;

        mainContent.addEventListener('touchstart', (e) => {
            this.touchStartY = e.touches[0].clientY;
            this.touchStartX = e.touches[0].clientX;
            this.isScrolling = false;
        });

        mainContent.addEventListener('touchmove', (e) => {
            if (!this.isScrolling) {
                const touchY = e.touches[0].clientY;
                const touchX = e.touches[0].clientX;
                const deltaY = Math.abs(touchY - this.touchStartY);
                const deltaX = Math.abs(touchX - this.touchStartX);

                if (deltaY > deltaX) {
                    this.isScrolling = true;
                }
            }
        });

        // Swipe gestures for sidebar control
        mainContent.addEventListener('touchend', (e) => {
            if (this.isScrolling) return;

            const touchEndY = e.changedTouches[0].clientY;
            const touchEndX = e.changedTouches[0].clientX;
            const deltaY = touchEndY - this.touchStartY;
            const deltaX = touchEndX - this.touchStartX;

            // Horizontal swipes for sidebar toggle
            if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 30) {
                if (deltaX > 0) {
                    // Swipe right - open menu
                    if (!this.sidebarStates.left) {
                        this.toggleSidebar('left');
                    }
                }
                // Removed swipe left functionality since there's only one sidebar
            }
        });
    }

    setupKeyboardOptimizations() {
        // Optimize virtual keyboard behavior
        const inputs = document.querySelectorAll('.mobile-input');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                // Scroll input into view when virtual keyboard appears
                setTimeout(() => {
                    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    }

    optimizeForMobile() {
        // Add mobile-specific meta tags if not present
        this.addMobileMetaTags();

        // Optimize images for mobile
        this.optimizeImages();

        // Setup performance monitoring
        this.setupPerformanceMonitoring();
    }

    addMobileMetaTags() {
        const head = document.head;

        // Viewport meta tag (should already exist but ensure it's optimized)
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

        // Touch icon
        if (!document.querySelector('link[rel="apple-touch-icon"]')) {
            const touchIcon = document.createElement('link');
            touchIcon.rel = 'apple-touch-icon';
            touchIcon.href = '/static/img/favicons/favicon.ico';
            head.appendChild(touchIcon);
        }

        // Theme color
        if (!document.querySelector('meta[name="theme-color"]')) {
            const themeColor = document.createElement('meta');
            themeColor.name = 'theme-color';
            themeColor.content = '#667eea';
            head.appendChild(themeColor);
        }
    }

    optimizeImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.loading) {
                img.loading = 'lazy';
            }
        });
    }

    setupPerformanceMonitoring() {
        // Monitor frame rate
        let lastTime = performance.now();
        let frameCount = 0;

        const checkPerformance = (currentTime) => {
            frameCount++;
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                if (fps < 30) {
                    console.warn('[Mobile Professional] Low frame rate detected:', fps, 'fps');
                }
                frameCount = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(checkPerformance);
        };

        requestAnimationFrame(checkPerformance);
    }

    handleOrientationChange() {
        // Adjust layout for orientation change
        const isLandscape = window.orientation === 90 || window.orientation === -90;
        document.body.classList.toggle('landscape', isLandscape);

        // Close sidebar on orientation change to prevent layout issues
        if (this.sidebarStates.left) {
            this.toggleSidebar('left');
        }
    }

    handleResize() {
        const newIsMobile = window.innerWidth <= 992;
        if (newIsMobile !== this.isMobile) {
            // Device type changed, reload page for proper layout
            window.location.reload();
        }
    }

    handleKeyboard(e) {
        // Escape key closes sidebar
        if (e.key === 'Escape') {
            if (this.sidebarStates.left) this.toggleSidebar('left');
        }

        // Alt + L for menu sidebar
        if (e.altKey && e.key === 'l') {
            e.preventDefault();
            this.toggleSidebar('left');
        }
    }

    async handlePreview() {
        const promptInput = document.getElementById('mobile-prompt-input');
        if (!promptInput) return;

        const prompt = promptInput.value.trim();
        if (!prompt) {
            this.showToast('Please enter a prompt first', 'error');
            return;
        }

        // Show loading state
        this.showLoading('Generating preview...');

        try {
            // Sync with desktop prompt input
            const desktopPromptInput = document.getElementById('prompt-input');
            if (desktopPromptInput) {
                desktopPromptInput.value = prompt;
            }

            // Call the preview API directly
            const response = await fetch('/preview_response', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    prompt: prompt,
                    task_id: this.getCurrentTaskId()
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Show the mobile preview container
                this.showMobilePreview(data.ai_response, data.quality_score || 'GOOD');
                this.hideLoading();
                this.showToast('Preview generated successfully!', 'success');
            } else {
                throw new Error(data.message || 'Preview generation failed');
            }

        } catch (error) {
            console.error('Preview error:', error);
            this.hideLoading();
            this.showToast('Failed to generate preview. Please try again.', 'error');
        }
    }

    showMobilePreview(aiResponse, qualityScore = 'GOOD') {
        const previewContainer = document.getElementById('mobile-preview-container');
        const previewText = document.getElementById('mobile-preview-text');
        const qualityScoreElement = document.getElementById('mobile-quality-score');

        if (previewContainer && previewText) {
            // Set the preview text
            previewText.textContent = aiResponse;

            // Set quality score
            if (qualityScoreElement) {
                qualityScoreElement.textContent = qualityScore;
                qualityScoreElement.className = `mobile-quality-score quality-${qualityScore.toLowerCase()}`;
            }

            // Show the preview container
            previewContainer.classList.remove('hidden');

            // Hide the input area temporarily
            const inputArea = document.querySelector('.mobile-input-area');
            if (inputArea) {
                inputArea.style.display = 'none';
            }

            // Store the current response for editing/submission
            this.currentPreviewResponse = aiResponse;

            // Scroll to preview
            previewContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    hideMobilePreview() {
        const previewContainer = document.getElementById('mobile-preview-container');
        const editContainer = document.getElementById('mobile-edit-container');
        const inputArea = document.querySelector('.mobile-input-area');

        if (previewContainer) {
            previewContainer.classList.add('hidden');
        }

        if (editContainer) {
            editContainer.classList.add('hidden');
        }

        if (inputArea) {
            inputArea.style.display = 'block';
        }

        this.currentPreviewResponse = null;
    }

    showMobileEdit() {
        const previewContainer = document.getElementById('mobile-preview-container');
        const editContainer = document.getElementById('mobile-edit-container');
        const editTextarea = document.getElementById('mobile-edit-text');

        if (previewContainer && editContainer && editTextarea) {
            // Hide preview, show edit
            previewContainer.classList.add('hidden');
            editContainer.classList.remove('hidden');

            // Set the current response in the textarea
            editTextarea.value = this.currentPreviewResponse || '';

            // Focus the textarea
            editTextarea.focus();

            // Scroll to edit container
            editContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    backToPreview() {
        const previewContainer = document.getElementById('mobile-preview-container');
        const editContainer = document.getElementById('mobile-edit-container');

        if (previewContainer && editContainer) {
            // Hide edit, show preview
            editContainer.classList.add('hidden');
            previewContainer.classList.remove('hidden');

            // Scroll to preview
            previewContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    handlePreviewResponse(data) {
        if (data && data.status === 'success') {
            // Update mobile messages with the preview
            if (data.ai_response) {
                this.addMobileMessage('ai', data.ai_response);

                // Show submit button after successful preview
                const submitBtn = document.getElementById('mobile-submit-btn');
                if (submitBtn) {
                    submitBtn.style.display = 'inline-flex';
                }
            }

            // Update game state if provided
            if (data.game_state) {
                this.updateMobileGameState(data.game_state);
            }
        }
    }

    async handleSubmitPreview() {
        if (!this.currentPreviewResponse) {
            this.showToast('No preview available to submit', 'error');
            return;
        }

        // Show loading state
        this.showLoading('Submitting response...');

        try {
            // Submit the current preview response
            const response = await fetch('/submit_response', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    response: this.currentPreviewResponse,
                    task_id: this.getCurrentTaskId()
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Add the response to messages
                this.addMobileMessage('ai', this.currentPreviewResponse);

                // Hide preview and show input area
                this.hideMobilePreview();

                // Clear the input
                const promptInput = document.getElementById('mobile-prompt-input');
                if (promptInput) {
                    promptInput.value = '';
                }

                this.hideLoading();
                this.showToast('Response submitted successfully!', 'success');

                // Update game state if provided
                if (data.game_state) {
                    this.updateMobileGameState(data.game_state);
                }
            } else {
                throw new Error(data.message || 'Submission failed');
            }

        } catch (error) {
            console.error('Submit error:', error);
            this.hideLoading();
            this.showToast('Failed to submit response. Please try again.', 'error');
        }
    }

    async handleSubmitEdited() {
        const editTextarea = document.getElementById('mobile-edit-text');
        if (!editTextarea) return;

        const editedResponse = editTextarea.value.trim();
        if (!editedResponse) {
            this.showToast('Please enter a response', 'error');
            return;
        }

        // Show loading state
        this.showLoading('Submitting edited response...');

        try {
            // Submit the edited response
            const response = await fetch('/submit_response', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    response: editedResponse,
                    task_id: this.getCurrentTaskId()
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Add the response to messages
                this.addMobileMessage('ai', editedResponse);

                // Hide edit container and show input area
                this.hideMobilePreview();

                // Clear the input
                const promptInput = document.getElementById('mobile-prompt-input');
                if (promptInput) {
                    promptInput.value = '';
                }

                this.hideLoading();
                this.showToast('Edited response submitted successfully!', 'success');

                // Update game state if provided
                if (data.game_state) {
                    this.updateMobileGameState(data.game_state);
                }
            } else {
                throw new Error(data.message || 'Submission failed');
            }

        } catch (error) {
            console.error('Submit edited error:', error);
            this.hideLoading();
            this.showToast('Failed to submit edited response. Please try again.', 'error');
        }
    }

    async handleSubmit() {
        const promptInput = document.getElementById('mobile-prompt-input');
        if (!promptInput) return;

        const prompt = promptInput.value.trim();
        if (!prompt) {
            this.showToast('Please enter a prompt first', 'error');
            return;
        }

        // Show loading state
        this.showLoading('Submitting response...');

        try {
            // Sync with desktop prompt input
            const desktopPromptInput = document.getElementById('prompt-input');
            if (desktopPromptInput) {
                desktopPromptInput.value = prompt;
            }

            // Call the desktop game's submit function directly
            if (window.handleSubmitResponse) {
                await window.handleSubmitResponse();
            } else if (window.submitResponse) {
                await window.submitResponse(prompt);
            } else {
                // Use API endpoint directly
                const response = await fetch('/submit_prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        task_id: this.getCurrentTaskId()
                    })
                });

                const data = await response.json();
                this.handleSubmitResponse(data);
            }

            this.hideLoading();

        } catch (error) {
            console.error('Submit error:', error);
            this.hideLoading();
            this.showToast('Failed to submit response. Please try again.', 'error');
        }
    }

    handleSubmitResponse(data) {
        if (data && data.status === 'success') {
            // Clear the prompt input
            const promptInput = document.getElementById('mobile-prompt-input');
            if (promptInput) {
                promptInput.value = '';
            }

            // Hide submit button
            const submitBtn = document.getElementById('mobile-submit-btn');
            if (submitBtn) {
                submitBtn.style.display = 'none';
            }

            // Add success message
            this.addMobileMessage('system', 'Response submitted successfully!');

            // Update game state
            if (data.game_state) {
                this.updateMobileGameState(data.game_state);
            }

            // Show success toast
            this.showToast('Response submitted successfully!', 'success');

            // Check if game is completed
            if (data.game_completed) {
                setTimeout(() => {
                    this.showGameCompleteModal(data);
                }, 1000);
            }

        } else {
            this.showToast(data?.message || 'Failed to submit response', 'error');
        }
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    getCurrentTaskId() {
        // Try to get task ID from various sources
        if (window.gameState && window.gameState.currentTask) {
            return window.gameState.currentTask;
        }

        // Fallback to default
        return 'cover_letter';
    }

    showGameCompleteModal(data) {
        const modalContent = `
            <h3>🎉 Congratulations!</h3>
            <p>You have successfully completed the challenge!</p>
            ${data.performance_score ? `<p><strong>Final Score:</strong> ${data.performance_score}</p>` : ''}
            ${data.challenges_completed ? `<p><strong>Challenges Completed:</strong> ${data.challenges_completed}</p>` : ''}
            <div class="mobile-button-group">
                <button class="mobile-btn mobile-btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo"></i> Play Again
                </button>
                <button class="mobile-btn mobile-btn-secondary" onclick="closeMobileModal()">
                    <i class="fas fa-times"></i> Close
                </button>
            </div>
        `;

        showMobileModal('Game Complete', modalContent);
    }

    handleRestart() {
        if (confirm('Are you sure you want to restart the game? All progress will be lost.')) {
            // Try multiple restart methods
            if (window.restartGame) {
                window.restartGame();
            } else if (document.getElementById('restart-game-button-main')) {
                document.getElementById('restart-game-button-main').click();
            } else if (document.getElementById('restart-game-button-sidebar')) {
                document.getElementById('restart-game-button-sidebar').click();
            }
        }
    }

    handleContinue() {
        // Try to continue the game
        if (window.continueGame) {
            window.continueGame();
        } else if (document.getElementById('continue-game-button')) {
            document.getElementById('continue-game-button').click();
        } else {
            // If no continue function, just close sidebars and focus on input
            this.toggleSidebar('left');
            const promptInput = document.getElementById('mobile-prompt-input');
            if (promptInput) {
                promptInput.focus();
            }
        }
    }

    // Integration methods for existing game functionality
    syncWithDesktopGame() {
        // Sync prompt input
        const mobilePromptInput = document.getElementById('mobile-prompt-input');
        const desktopPromptInput = document.getElementById('prompt-input');

        if (mobilePromptInput && desktopPromptInput) {
            // Sync mobile to desktop
            mobilePromptInput.addEventListener('input', () => {
                desktopPromptInput.value = mobilePromptInput.value;
            });

            // Sync desktop to mobile
            desktopPromptInput.addEventListener('input', () => {
                mobilePromptInput.value = desktopPromptInput.value;
            });
        }

        // Listen for desktop game events
        this.setupDesktopEventListeners();

        // Sync messages
        this.syncMessages();

        // Sync game state
        this.syncGameState();
    }

    setupDesktopEventListeners() {
        // Listen for desktop game state changes
        if (window.gameState) {
            // Create a proxy to watch for changes
            const originalGameState = window.gameState;
            const self = this;

            // Watch for changes to game state
            const gameStateHandler = {
                set(target, property, value) {
                    target[property] = value;
                    // Update mobile UI when desktop game state changes
                    self.updateMobileGameState(target);
                    return true;
                }
            };

            // Only proxy if not already proxied
            if (!window.gameState._isMobileProxied) {
                window.gameState = new Proxy(originalGameState, gameStateHandler);
                window.gameState._isMobileProxied = true;
            }
        }

        // Listen for desktop message updates
        const desktopMessagesContainer = document.querySelector('.messages-container');
        if (desktopMessagesContainer) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        // New messages added to desktop, sync to mobile
                        this.syncMessages();
                    }
                });
            });

            observer.observe(desktopMessagesContainer, {
                childList: true,
                subtree: true
            });
        }

        // Listen for desktop button state changes
        this.watchDesktopButtons();
    }

    watchDesktopButtons() {
        // Watch for preview button clicks
        const previewButton = document.getElementById('preview-button');
        if (previewButton) {
            previewButton.addEventListener('click', () => {
                this.showLoading('Generating preview...');
            });
        }

        // Watch for submit button clicks
        const submitButton = document.getElementById('submit-button');
        if (submitButton) {
            submitButton.addEventListener('click', () => {
                this.showLoading('Submitting response...');
            });
        }
    }

    syncMessages() {
        const desktopMessagesContainer = document.querySelector('.messages-container');
        const mobileMessagesContainer = document.querySelector('.mobile-messages-container');

        if (!desktopMessagesContainer || !mobileMessagesContainer) return;

        // Create observer to watch for new messages
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    this.updateMobileMessages();
                }
            });
        });

        observer.observe(desktopMessagesContainer, {
            childList: true,
            subtree: true
        });

        // Initial sync
        this.updateMobileMessages();
    }

    updateMobileMessages() {
        const desktopMessages = document.querySelectorAll('.messages-container .message');
        const mobileMessagesContainer = document.getElementById('mobile-messages-container');

        if (!mobileMessagesContainer) return;

        // Clear mobile messages
        mobileMessagesContainer.innerHTML = '';

        // Copy and transform desktop messages for mobile
        desktopMessages.forEach((message, index) => {
            const mobileMessage = this.createMobileMessage(message);
            mobileMessagesContainer.appendChild(mobileMessage);
        });

        // Scroll to bottom
        mobileMessagesContainer.scrollTop = mobileMessagesContainer.scrollHeight;
    }

    createMobileMessage(desktopMessage) {
        const mobileMessage = document.createElement('div');
        mobileMessage.className = 'mobile-message';

        // Determine message type
        if (desktopMessage.classList.contains('user-message')) {
            mobileMessage.classList.add('user');
        } else if (desktopMessage.classList.contains('ai-message')) {
            mobileMessage.classList.add('ai');
        } else {
            mobileMessage.classList.add('system');
        }

        // Create message content
        const messageContent = document.createElement('div');
        messageContent.className = 'mobile-message-content';
        messageContent.innerHTML = desktopMessage.innerHTML;

        // Add timestamp if available
        const timestamp = desktopMessage.querySelector('.message-time');
        if (timestamp) {
            const mobileHeader = document.createElement('div');
            mobileHeader.className = 'mobile-message-header';

            const mobileTime = document.createElement('span');
            mobileTime.className = 'mobile-message-time';
            mobileTime.textContent = timestamp.textContent;

            mobileHeader.appendChild(mobileTime);
            mobileMessage.appendChild(mobileHeader);
        }

        mobileMessage.appendChild(messageContent);
        return mobileMessage;
    }

    syncGameState() {
        // Sync character info
        this.syncCharacterInfo();

        // Sync game stats
        this.syncGameStats();

        // Set up periodic sync
        setInterval(() => {
            this.syncCharacterInfo();
            this.syncGameStats();
        }, 5000); // Sync every 5 seconds
    }

    syncCharacterInfo() {
        const desktopCharacterName = document.getElementById('character-name');
        const desktopCharacterTitle = document.getElementById('character-title');
        const desktopCurrentManager = document.getElementById('current-manager');
        const desktopCurrentTask = document.getElementById('current-task');

        const mobileCharacterName = document.getElementById('mobile-character-name');
        const mobileCharacterTitle = document.getElementById('mobile-character-title');
        const mobileCurrentManager = document.getElementById('mobile-current-manager');
        const mobileCurrentTask = document.getElementById('mobile-current-task');

        if (desktopCharacterName && mobileCharacterName) {
            mobileCharacterName.textContent = desktopCharacterName.textContent;
        }

        if (desktopCharacterTitle && mobileCharacterTitle) {
            mobileCharacterTitle.textContent = desktopCharacterTitle.textContent;
        }

        if (desktopCurrentManager && mobileCurrentManager) {
            mobileCurrentManager.textContent = desktopCurrentManager.textContent;
        }

        if (desktopCurrentTask && mobileCurrentTask) {
            mobileCurrentTask.textContent = desktopCurrentTask.textContent;
        }
    }

    syncGameStats() {
        const statsMapping = [
            { desktop: '#current-role', mobile: '#mobile-current-role' },
            { desktop: '#performance-score', mobile: '#mobile-performance-score' },
            { desktop: '#challenges-completed', mobile: '#mobile-challenges-completed' },
            { desktop: '#role-progress', mobile: '#mobile-role-progress' }
        ];

        statsMapping.forEach(({ desktop, mobile }) => {
            const desktopElement = document.querySelector(desktop);
            const mobileElement = document.querySelector(mobile);

            if (desktopElement && mobileElement) {
                mobileElement.textContent = desktopElement.textContent;
            }
        });

        // Sync progress bar
        const desktopProgressBar = document.getElementById('progress-bar');
        const mobileProgressBar = document.getElementById('mobile-progress-bar');

        if (desktopProgressBar && mobileProgressBar) {
            const width = desktopProgressBar.style.width;
            mobileProgressBar.style.width = width;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `mobile-toast mobile-toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.style.position = 'absolute';
        announcement.style.left = '-10000px';
        announcement.style.width = '1px';
        announcement.style.height = '1px';
        announcement.style.overflow = 'hidden';
        announcement.textContent = message;

        document.body.appendChild(announcement);
        setTimeout(() => announcement.remove(), 1000);
    }

    // Mobile-specific game integration methods
    showLoading(message = 'Loading...') {
        // Show loading overlay
        let loadingOverlay = document.getElementById('mobile-loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'mobile-loading-overlay';
            loadingOverlay.className = 'mobile-loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="mobile-loading-content">
                    <div class="mobile-spinner"></div>
                    <div class="mobile-loading-text">${message}</div>
                </div>
            `;
            document.body.appendChild(loadingOverlay);
        } else {
            loadingOverlay.querySelector('.mobile-loading-text').textContent = message;
            loadingOverlay.style.display = 'flex';
        }

        // Also trigger desktop loading if available
        if (window.showLoading) {
            window.showLoading();
        }
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('mobile-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        // Also hide desktop loading if available
        if (window.hideLoading) {
            window.hideLoading();
        }
    }

    getCSRFToken() {
        // Get CSRF token from meta tag or cookie
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) {
            return csrfMeta.getAttribute('content');
        }

        // Fallback: get from cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }

        // Fallback: get from form input
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput) {
            return csrfInput.value;
        }

        return '';
    }

    getCurrentTaskId() {
        // Try to get task ID from various sources
        const taskIdElement = document.getElementById('current-task-id');
        if (taskIdElement) {
            return taskIdElement.value || taskIdElement.textContent;
        }

        // Try to get from game state
        if (window.gameState && window.gameState.currentTask) {
            return window.gameState.currentTask.id;
        }

        // Try to get from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const taskId = urlParams.get('task_id');
        if (taskId) {
            return taskId;
        }

        // Default fallback
        return '1';
    }

    addMobileMessage(sender, text, timestamp = null) {
        const messagesContainer = document.getElementById('mobile-messages-container');
        if (!messagesContainer) return;

        const messageElement = document.createElement('div');
        messageElement.className = `mobile-message mobile-message-${sender}`;

        const timeStr = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

        messageElement.innerHTML = `
            <div class="mobile-message-content">
                <div class="mobile-message-text">${text}</div>
                <div class="mobile-message-time">${timeStr}</div>
            </div>
        `;

        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Also add to desktop messages if available
        if (window.addMessageToUI) {
            window.addMessageToUI({
                sender: sender,
                text: text,
                timestamp: timestamp || new Date().toISOString()
            });
        }
    }

    updateMobileGameState(gameState) {
        // Update mobile UI elements with new game state
        if (gameState.current_role) {
            const roleElement = document.getElementById('mobile-current-role');
            if (roleElement) {
                roleElement.textContent = gameState.current_role.replace('_', ' ').toUpperCase();
            }
        }

        if (gameState.performance_score !== undefined) {
            const scoreElement = document.getElementById('mobile-performance-score');
            if (scoreElement) {
                scoreElement.textContent = gameState.performance_score;
            }
        }

        if (gameState.challenges_completed !== undefined) {
            const challengesElement = document.getElementById('mobile-challenges-completed');
            if (challengesElement) {
                challengesElement.textContent = gameState.challenges_completed;
            }
        }

        if (gameState.role_challenges_completed !== undefined && gameState.current_role) {
            const progressElement = document.getElementById('mobile-role-progress');
            if (progressElement) {
                // Get challenges required for current role (default to 3)
                const challengesRequired = this.getChallengesRequired(gameState.current_role);
                progressElement.textContent = `${gameState.role_challenges_completed}/${challengesRequired}`;

                // Update progress bar
                const progressBar = document.getElementById('mobile-progress-bar');
                if (progressBar) {
                    const percentage = (gameState.role_challenges_completed / challengesRequired) * 100;
                    progressBar.style.width = `${Math.min(percentage, 100)}%`;
                }
            }
        }

        // Update character info if available
        if (gameState.current_manager) {
            const managerElement = document.getElementById('mobile-current-manager');
            if (managerElement) {
                managerElement.textContent = gameState.current_manager.toUpperCase();
            }
        }

        if (gameState.current_task) {
            const taskElement = document.getElementById('mobile-current-task');
            if (taskElement) {
                taskElement.textContent = gameState.current_task.replace('_', ' ').toUpperCase();
            }
        }

        // Update desktop game state if available
        if (window.gameState) {
            Object.assign(window.gameState, gameState);
        }
    }

    getChallengesRequired(role) {
        const roleRequirements = {
            'applicant': 3,
            'employee': 3,
            'senior_employee': 3,
            'manager': 3,
            'senior_manager': 3,
            'executive': 3
        };
        return roleRequirements[role] || 3;
    }
}

// Global functions for mobile help buttons
function showMobileHelp() {
    const helpContent = `
        <h3>How to Play Corporate Prompt Master</h3>
        <ol>
            <li><strong>Read the Challenge:</strong> Your contact will present you with a workplace scenario</li>
            <li><strong>Craft Your Prompt:</strong> Write a clear, specific prompt for the AI assistant</li>
            <li><strong>Preview Response:</strong> See how the AI responds to your prompt</li>
            <li><strong>Edit if Needed:</strong> Refine the response to match your style</li>
            <li><strong>Submit:</strong> Send your final response to advance</li>
        </ol>

        <h4>Tips for Success:</h4>
        <ul>
            <li>Be specific in your prompts</li>
            <li>Consider the workplace context</li>
            <li>Maintain professional tone</li>
            <li>Review responses before submitting</li>
        </ul>

        <h4>Navigation:</h4>
        <ul>
            <li>Tap header buttons to open sidebars</li>
            <li>Swipe right to open game info</li>
            <li>Swipe left to open career path</li>
            <li>Use keyboard shortcuts for quick access</li>
        </ul>
    `;

    showMobileModal('Game Guide', helpContent);
}

function showMobileFeedback() {
    const feedbackContent = `
        <h3>We Value Your Feedback</h3>
        <p>Help us improve the mobile gaming experience!</p>

        <div class="mobile-form-group">
            <label class="mobile-form-label" for="feedback-type">Feedback Type</label>
            <select id="feedback-type" class="mobile-input">
                <option value="bug">Bug Report</option>
                <option value="feature">Feature Request</option>
                <option value="ui">UI/UX Feedback</option>
                <option value="general">General Feedback</option>
            </select>
        </div>

        <div class="mobile-form-group">
            <label class="mobile-form-label" for="feedback-message">Your Feedback</label>
            <textarea id="feedback-message" class="mobile-input mobile-textarea" rows="4"
                placeholder="Tell us about your experience..."></textarea>
        </div>

        <div class="mobile-button-group">
            <button class="mobile-btn mobile-btn-primary" onclick="submitMobileFeedback()">
                <i class="fas fa-paper-plane"></i> Submit Feedback
            </button>
            <button class="mobile-btn mobile-btn-secondary" onclick="closeMobileModal()">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    `;

    showMobileModal('Feedback', feedbackContent);
}

function showMobileModal(title, content) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.mobile-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'mobile-modal';
    modal.innerHTML = `
        <div class="mobile-modal-content">
            <div class="mobile-modal-header">
                <h2 class="mobile-modal-title">${title}</h2>
                <button class="mobile-modal-close" onclick="closeMobileModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mobile-modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on backdrop click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeMobileModal();
        }
    });

    // Close on escape key
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeMobileModal();
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

function closeMobileModal() {
    const modal = document.querySelector('.mobile-modal');
    if (modal) {
        modal.remove();
    }
}

function submitMobileFeedback() {
    const type = document.getElementById('feedback-type')?.value;
    const message = document.getElementById('feedback-message')?.value;

    if (!message || message.trim().length < 10) {
        alert('Please provide more detailed feedback (at least 10 characters).');
        return;
    }

    // Here you would typically send the feedback to your server
    console.log('Mobile Feedback Submitted:', { type, message });

    // Show success message
    if (window.mobileProfessionalInterface) {
        window.mobileProfessionalInterface.showToast('Thank you for your feedback!', 'success');
    }

    closeMobileModal();
}

// Initialize mobile interface when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.innerWidth <= 992) {
        window.mobileProfessionalInterface = new MobileProfessionalInterface();
    }
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileProfessionalInterface;
}
