/**
 * Professional Mobile Interface JavaScript
 * Handles mobile-specific interactions and optimizations
 */

class MobileProfessionalInterface {
    constructor() {
        this.isMobile = window.innerWidth <= 992;
        this.isInitialized = false;
        this.sidebarStates = {
            left: false,
            right: false
        };
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.isScrolling = false;

        this.init();
    }

    init() {
        if (!this.isMobile) return;

        console.log('[Mobile Professional] Initializing mobile interface');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.createMobileStructure();
        this.setupEventListeners();
        this.optimizeForMobile();
        this.setupTouchGestures();
        this.setupKeyboardOptimizations();

        // Set up integration with existing game
        setTimeout(() => {
            this.syncWithDesktopGame();
        }, 1000); // Wait for other scripts to initialize

        this.isInitialized = true;

        console.log('[Mobile Professional] Mobile interface initialized');
    }

    createMobileStructure() {
        const body = document.body;
        const appContainer = document.querySelector('.app-container');

        if (!appContainer) return;

        // Create mobile header
        const mobileHeader = this.createMobileHeader();

        // Create mobile game container
        const mobileContainer = document.createElement('div');
        mobileContainer.className = 'mobile-game-container';

        // Transform existing sidebars
        const leftSidebar = this.transformLeftSidebar();
        const rightSidebar = this.transformRightSidebar();
        const mainContent = this.transformMainContent();

        // Append elements in mobile order
        mobileContainer.appendChild(leftSidebar);
        mobileContainer.appendChild(mainContent);
        mobileContainer.appendChild(rightSidebar);

        // Replace app container content
        appContainer.innerHTML = '';
        appContainer.appendChild(mobileHeader);
        appContainer.appendChild(mobileContainer);

        // Add mobile classes to body
        body.classList.add('mobile-professional');
    }

    createMobileHeader() {
        const header = document.createElement('header');
        header.className = 'mobile-header';

        // Left sidebar toggle
        const leftToggle = document.createElement('button');
        leftToggle.className = 'mobile-header-btn';
        leftToggle.innerHTML = '<i class="fas fa-bars"></i>';
        leftToggle.setAttribute('aria-label', 'Toggle game info');
        leftToggle.addEventListener('click', () => this.toggleSidebar('left'));

        // Title
        const title = document.createElement('h1');
        title.className = 'mobile-header-title';
        title.textContent = 'Corporate Prompt Master';

        // Company badge (if exists)
        const companyBadge = document.querySelector('.company-badge');
        if (companyBadge) {
            const mobileBadge = document.createElement('span');
            mobileBadge.className = 'mobile-company-badge';
            mobileBadge.textContent = companyBadge.textContent;
            header.appendChild(mobileBadge);
        }

        // Right sidebar toggle
        const rightToggle = document.createElement('button');
        rightToggle.className = 'mobile-header-btn';
        rightToggle.innerHTML = '<i class="fas fa-chart-org"></i>';
        rightToggle.setAttribute('aria-label', 'Toggle career path');
        rightToggle.addEventListener('click', () => this.toggleSidebar('right'));

        header.appendChild(leftToggle);
        header.appendChild(title);
        header.appendChild(rightToggle);

        return header;
    }

    transformLeftSidebar() {
        const originalSidebar = document.querySelector('.sidebar');
        const mobileSidebar = document.createElement('div');
        mobileSidebar.className = 'mobile-sidebar-left collapsed';

        if (originalSidebar) {
            // Copy content and transform for mobile
            const content = originalSidebar.cloneNode(true);
            this.optimizeSidebarContent(content);
            mobileSidebar.appendChild(content);
        }

        return mobileSidebar;
    }

    transformRightSidebar() {
        const originalRightSidebar = document.querySelector('.right-sidebar');
        const mobileSidebar = document.createElement('div');
        mobileSidebar.className = 'mobile-sidebar-right collapsed';

        if (originalRightSidebar) {
            // Copy content and transform for mobile
            const content = originalRightSidebar.cloneNode(true);
            this.optimizeSidebarContent(content);
            mobileSidebar.appendChild(content);
        }

        return mobileSidebar;
    }

    transformMainContent() {
        const originalMainContent = document.querySelector('.main-content');
        const mobileMainContent = document.createElement('div');
        mobileMainContent.className = 'mobile-main-content';

        if (originalMainContent) {
            // Create mobile messages container
            const messagesContainer = document.createElement('div');
            messagesContainer.className = 'mobile-messages-container';
            messagesContainer.id = 'mobile-messages-container';

            // Copy existing messages
            const originalMessages = originalMainContent.querySelector('.messages-container');
            if (originalMessages) {
                messagesContainer.innerHTML = originalMessages.innerHTML;
                this.optimizeMessages(messagesContainer);
            }

            // Create mobile input area
            const inputArea = this.createMobileInputArea();

            mobileMainContent.appendChild(messagesContainer);
            mobileMainContent.appendChild(inputArea);
        }

        return mobileMainContent;
    }

    createMobileInputArea() {
        const inputArea = document.createElement('div');
        inputArea.className = 'mobile-input-area';

        // Prompt input
        const promptGroup = document.createElement('div');
        promptGroup.className = 'mobile-form-group';

        const promptLabel = document.createElement('label');
        promptLabel.className = 'mobile-form-label';
        promptLabel.textContent = 'Your Prompt';
        promptLabel.setAttribute('for', 'mobile-prompt-input');

        const promptInput = document.createElement('textarea');
        promptInput.className = 'mobile-input mobile-textarea';
        promptInput.id = 'mobile-prompt-input';
        promptInput.placeholder = 'Enter your prompt here...';
        promptInput.rows = 3;

        promptGroup.appendChild(promptLabel);
        promptGroup.appendChild(promptInput);

        // Button group
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'mobile-button-group';

        const previewBtn = document.createElement('button');
        previewBtn.className = 'mobile-btn mobile-btn-primary';
        previewBtn.innerHTML = '<i class="fas fa-eye"></i> Preview';
        previewBtn.addEventListener('click', () => this.handlePreview());

        const restartBtn = document.createElement('button');
        restartBtn.className = 'mobile-btn mobile-btn-secondary';
        restartBtn.innerHTML = '<i class="fas fa-redo"></i> Restart';
        restartBtn.addEventListener('click', () => this.handleRestart());

        buttonGroup.appendChild(previewBtn);
        buttonGroup.appendChild(restartBtn);

        inputArea.appendChild(promptGroup);
        inputArea.appendChild(buttonGroup);

        return inputArea;
    }

    optimizeSidebarContent(content) {
        // Add mobile-specific classes to sidebar content
        const stats = content.querySelectorAll('.stat');
        if (stats.length > 0) {
            const statsContainer = document.createElement('div');
            statsContainer.className = 'mobile-stat-grid';

            stats.forEach(stat => {
                const mobileStatItem = document.createElement('div');
                mobileStatItem.className = 'mobile-stat-item';
                mobileStatItem.innerHTML = stat.innerHTML;
                statsContainer.appendChild(mobileStatItem);
            });

            // Replace original stats with mobile version
            const parentElement = stats[0].parentElement;
            stats.forEach(stat => stat.remove());
            parentElement.appendChild(statsContainer);
        }

        // Optimize buttons for mobile
        const buttons = content.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.classList.add('mobile-btn');
            if (btn.classList.contains('primary-button')) {
                btn.classList.add('mobile-btn-primary');
            } else if (btn.classList.contains('secondary-button')) {
                btn.classList.add('mobile-btn-secondary');
            }
        });
    }

    optimizeMessages(container) {
        const messages = container.querySelectorAll('.message');
        messages.forEach(message => {
            message.classList.add('mobile-message');

            // Add mobile-specific message classes
            if (message.classList.contains('user-message')) {
                message.classList.add('user');
            } else if (message.classList.contains('ai-message')) {
                message.classList.add('ai');
            } else {
                message.classList.add('system');
            }
        });
    }

    toggleSidebar(side) {
        const sidebar = document.querySelector(`.mobile-sidebar-${side}`);
        if (!sidebar) return;

        const isExpanded = !sidebar.classList.contains('collapsed');

        if (isExpanded) {
            // Collapse
            sidebar.classList.add('collapsed');
            sidebar.classList.remove('expanded');
            this.sidebarStates[side] = false;
        } else {
            // Expand (and collapse the other sidebar)
            const otherSide = side === 'left' ? 'right' : 'left';
            const otherSidebar = document.querySelector(`.mobile-sidebar-${otherSide}`);

            if (otherSidebar) {
                otherSidebar.classList.add('collapsed');
                otherSidebar.classList.remove('expanded');
                this.sidebarStates[otherSide] = false;
            }

            sidebar.classList.remove('collapsed');
            sidebar.classList.add('expanded');
            this.sidebarStates[side] = true;
        }

        // Update header button states
        this.updateHeaderButtonStates();

        // Announce to screen readers
        const action = isExpanded ? 'collapsed' : 'expanded';
        this.announceToScreenReader(`${side} sidebar ${action}`);
    }

    updateHeaderButtonStates() {
        const leftBtn = document.querySelector('.mobile-header-btn:first-child');
        const rightBtn = document.querySelector('.mobile-header-btn:last-child');

        if (leftBtn) {
            leftBtn.classList.toggle('active', this.sidebarStates.left);
        }
        if (rightBtn) {
            rightBtn.classList.toggle('active', this.sidebarStates.right);
        }
    }

    setupEventListeners() {
        // Orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 300);
        });

        // Resize
        window.addEventListener('resize', () => this.handleResize());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Prevent zoom on double tap
        document.addEventListener('touchend', (e) => {
            if (e.target.closest('.mobile-input, .mobile-btn')) {
                e.preventDefault();
            }
        });
    }

    setupTouchGestures() {
        const mainContent = document.querySelector('.mobile-main-content');
        if (!mainContent) return;

        mainContent.addEventListener('touchstart', (e) => {
            this.touchStartY = e.touches[0].clientY;
            this.touchStartX = e.touches[0].clientX;
            this.isScrolling = false;
        });

        mainContent.addEventListener('touchmove', (e) => {
            if (!this.isScrolling) {
                const touchY = e.touches[0].clientY;
                const touchX = e.touches[0].clientX;
                const deltaY = Math.abs(touchY - this.touchStartY);
                const deltaX = Math.abs(touchX - this.touchStartX);

                if (deltaY > deltaX) {
                    this.isScrolling = true;
                }
            }
        });

        // Swipe gestures for sidebar control
        mainContent.addEventListener('touchend', (e) => {
            if (this.isScrolling) return;

            const touchEndY = e.changedTouches[0].clientY;
            const touchEndX = e.changedTouches[0].clientX;
            const deltaY = touchEndY - this.touchStartY;
            const deltaX = touchEndX - this.touchStartX;

            // Horizontal swipes for sidebar toggle
            if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 30) {
                if (deltaX > 0) {
                    // Swipe right - open left sidebar
                    if (!this.sidebarStates.left) {
                        this.toggleSidebar('left');
                    }
                } else {
                    // Swipe left - open right sidebar
                    if (!this.sidebarStates.right) {
                        this.toggleSidebar('right');
                    }
                }
            }
        });
    }

    setupKeyboardOptimizations() {
        // Optimize virtual keyboard behavior
        const inputs = document.querySelectorAll('.mobile-input');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                // Scroll input into view when virtual keyboard appears
                setTimeout(() => {
                    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    }

    optimizeForMobile() {
        // Add mobile-specific meta tags if not present
        this.addMobileMetaTags();

        // Optimize images for mobile
        this.optimizeImages();

        // Setup performance monitoring
        this.setupPerformanceMonitoring();
    }

    addMobileMetaTags() {
        const head = document.head;

        // Viewport meta tag (should already exist but ensure it's optimized)
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

        // Touch icon
        if (!document.querySelector('link[rel="apple-touch-icon"]')) {
            const touchIcon = document.createElement('link');
            touchIcon.rel = 'apple-touch-icon';
            touchIcon.href = '/static/img/favicons/favicon.ico';
            head.appendChild(touchIcon);
        }

        // Theme color
        if (!document.querySelector('meta[name="theme-color"]')) {
            const themeColor = document.createElement('meta');
            themeColor.name = 'theme-color';
            themeColor.content = '#667eea';
            head.appendChild(themeColor);
        }
    }

    optimizeImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.loading) {
                img.loading = 'lazy';
            }
        });
    }

    setupPerformanceMonitoring() {
        // Monitor frame rate
        let lastTime = performance.now();
        let frameCount = 0;

        const checkPerformance = (currentTime) => {
            frameCount++;
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                if (fps < 30) {
                    console.warn('[Mobile Professional] Low frame rate detected:', fps, 'fps');
                }
                frameCount = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(checkPerformance);
        };

        requestAnimationFrame(checkPerformance);
    }

    handleOrientationChange() {
        // Adjust layout for orientation change
        const isLandscape = window.orientation === 90 || window.orientation === -90;
        document.body.classList.toggle('landscape', isLandscape);

        // Close sidebars on orientation change to prevent layout issues
        if (this.sidebarStates.left || this.sidebarStates.right) {
            this.toggleSidebar('left');
            this.toggleSidebar('right');
        }
    }

    handleResize() {
        const newIsMobile = window.innerWidth <= 992;
        if (newIsMobile !== this.isMobile) {
            // Device type changed, reload page for proper layout
            window.location.reload();
        }
    }

    handleKeyboard(e) {
        // Escape key closes sidebars
        if (e.key === 'Escape') {
            if (this.sidebarStates.left) this.toggleSidebar('left');
            if (this.sidebarStates.right) this.toggleSidebar('right');
        }

        // Alt + L for left sidebar
        if (e.altKey && e.key === 'l') {
            e.preventDefault();
            this.toggleSidebar('left');
        }

        // Alt + R for right sidebar
        if (e.altKey && e.key === 'r') {
            e.preventDefault();
            this.toggleSidebar('right');
        }
    }

    handlePreview() {
        const promptInput = document.getElementById('mobile-prompt-input');
        if (!promptInput) return;

        const prompt = promptInput.value.trim();
        if (!prompt) {
            this.showToast('Please enter a prompt first');
            return;
        }

        // Sync with desktop prompt input
        const desktopPromptInput = document.getElementById('prompt-input');
        if (desktopPromptInput) {
            desktopPromptInput.value = prompt;
        }

        // Trigger preview functionality (integrate with existing game logic)
        if (window.previewResponse) {
            window.previewResponse();
        } else if (document.getElementById('preview-button')) {
            document.getElementById('preview-button').click();
        }
    }

    handleRestart() {
        if (confirm('Are you sure you want to restart the game? All progress will be lost.')) {
            // Try multiple restart methods
            if (window.restartGame) {
                window.restartGame();
            } else if (document.getElementById('restart-game-button-main')) {
                document.getElementById('restart-game-button-main').click();
            } else if (document.getElementById('restart-game-button-sidebar')) {
                document.getElementById('restart-game-button-sidebar').click();
            }
        }
    }

    // Integration methods for existing game functionality
    syncWithDesktopGame() {
        // Sync prompt input
        const mobilePromptInput = document.getElementById('mobile-prompt-input');
        const desktopPromptInput = document.getElementById('prompt-input');

        if (mobilePromptInput && desktopPromptInput) {
            // Sync mobile to desktop
            mobilePromptInput.addEventListener('input', () => {
                desktopPromptInput.value = mobilePromptInput.value;
            });

            // Sync desktop to mobile
            desktopPromptInput.addEventListener('input', () => {
                mobilePromptInput.value = desktopPromptInput.value;
            });
        }

        // Sync messages
        this.syncMessages();

        // Sync game state
        this.syncGameState();
    }

    syncMessages() {
        const desktopMessagesContainer = document.querySelector('.messages-container');
        const mobileMessagesContainer = document.querySelector('.mobile-messages-container');

        if (!desktopMessagesContainer || !mobileMessagesContainer) return;

        // Create observer to watch for new messages
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    this.updateMobileMessages();
                }
            });
        });

        observer.observe(desktopMessagesContainer, {
            childList: true,
            subtree: true
        });

        // Initial sync
        this.updateMobileMessages();
    }

    updateMobileMessages() {
        const desktopMessages = document.querySelectorAll('.messages-container .message');
        const mobileMessagesContainer = document.querySelector('.mobile-messages-container');

        if (!mobileMessagesContainer) return;

        // Clear mobile messages
        mobileMessagesContainer.innerHTML = '';

        // Copy and transform desktop messages for mobile
        desktopMessages.forEach((message, index) => {
            const mobileMessage = this.createMobileMessage(message);
            mobileMessagesContainer.appendChild(mobileMessage);
        });

        // Scroll to bottom
        mobileMessagesContainer.scrollTop = mobileMessagesContainer.scrollHeight;
    }

    createMobileMessage(desktopMessage) {
        const mobileMessage = document.createElement('div');
        mobileMessage.className = 'mobile-message';

        // Determine message type
        if (desktopMessage.classList.contains('user-message')) {
            mobileMessage.classList.add('user');
        } else if (desktopMessage.classList.contains('ai-message')) {
            mobileMessage.classList.add('ai');
        } else {
            mobileMessage.classList.add('system');
        }

        // Create message content
        const messageContent = document.createElement('div');
        messageContent.className = 'mobile-message-content';
        messageContent.innerHTML = desktopMessage.innerHTML;

        // Add timestamp if available
        const timestamp = desktopMessage.querySelector('.message-time');
        if (timestamp) {
            const mobileHeader = document.createElement('div');
            mobileHeader.className = 'mobile-message-header';

            const mobileTime = document.createElement('span');
            mobileTime.className = 'mobile-message-time';
            mobileTime.textContent = timestamp.textContent;

            mobileHeader.appendChild(mobileTime);
            mobileMessage.appendChild(mobileHeader);
        }

        mobileMessage.appendChild(messageContent);
        return mobileMessage;
    }

    syncGameState() {
        // Sync character info
        this.syncCharacterInfo();

        // Sync game stats
        this.syncGameStats();

        // Set up periodic sync
        setInterval(() => {
            this.syncCharacterInfo();
            this.syncGameStats();
        }, 5000); // Sync every 5 seconds
    }

    syncCharacterInfo() {
        const desktopCharacterName = document.getElementById('character-name');
        const desktopCharacterTitle = document.getElementById('character-title');

        const mobileCharacterName = document.querySelector('.mobile-sidebar-left #character-name');
        const mobileCharacterTitle = document.querySelector('.mobile-sidebar-left #character-title');

        if (desktopCharacterName && mobileCharacterName) {
            mobileCharacterName.textContent = desktopCharacterName.textContent;
        }

        if (desktopCharacterTitle && mobileCharacterTitle) {
            mobileCharacterTitle.textContent = desktopCharacterTitle.textContent;
        }
    }

    syncGameStats() {
        const statsMapping = [
            { desktop: '#current-role', mobile: '.mobile-sidebar-left #current-role' },
            { desktop: '#performance-score', mobile: '.mobile-sidebar-left #performance-score' },
            { desktop: '#challenges-completed', mobile: '.mobile-sidebar-left #challenges-completed' },
            { desktop: '#role-progress', mobile: '.mobile-sidebar-left #role-progress' }
        ];

        statsMapping.forEach(({ desktop, mobile }) => {
            const desktopElement = document.querySelector(desktop);
            const mobileElement = document.querySelector(mobile);

            if (desktopElement && mobileElement) {
                mobileElement.textContent = desktopElement.textContent;
            }
        });

        // Sync progress bar
        const desktopProgressBar = document.getElementById('progress-bar');
        const mobileProgressBar = document.querySelector('.mobile-progress-bar');

        if (desktopProgressBar && mobileProgressBar) {
            const width = desktopProgressBar.style.width;
            mobileProgressBar.style.width = width;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `mobile-toast mobile-toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.style.position = 'absolute';
        announcement.style.left = '-10000px';
        announcement.style.width = '1px';
        announcement.style.height = '1px';
        announcement.style.overflow = 'hidden';
        announcement.textContent = message;

        document.body.appendChild(announcement);
        setTimeout(() => announcement.remove(), 1000);
    }
}

// Initialize mobile interface when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.innerWidth <= 992) {
        window.mobileProfessionalInterface = new MobileProfessionalInterface();
    }
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileProfessionalInterface;
}
