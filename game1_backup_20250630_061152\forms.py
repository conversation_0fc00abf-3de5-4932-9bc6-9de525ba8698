from django import forms
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.text import slugify

from .models import CompanyGameSettings, CompanyCourse, CourseTask
from corporate.models import Company


class CompanyRegistrationForm(forms.ModelForm):
    """Form for registering a new company"""

    class Meta:
        model = Company
        fields = ['name']

    def clean_name(self):
        name = self.cleaned_data['name']
        slug = slugify(name)

        # Check if slug already exists
        if Company.objects.filter(slug=slug).exists():
            raise ValidationError("A company with this name already exists.")

        return name

    def save(self, commit=True, user=None):
        company = super().save(commit=False)
        company.slug = slugify(company.name)

        if user:
            company.owner = user

        if commit:
            company.save()

            # Create default game settings
            CompanyGameSettings.objects.create(company=company)

        return company


class CompanyGameSettingsForm(forms.ModelForm):
    """Form for updating company game settings"""

    class Meta:
        model = CompanyGameSettings
        fields = [
            'is_public', 'require_login', 'show_leaderboard',
            'show_in_global_leaderboard', 'custom_welcome_message',
            'custom_completion_message', 'use_company_branding',
            'primary_color'
        ]
        widgets = {
            'primary_color': forms.TextInput(attrs={'type': 'color'}),
            'custom_welcome_message': forms.Textarea(attrs={'rows': 3}),
            'custom_completion_message': forms.Textarea(attrs={'rows': 3}),
        }


class CompanyCourseForm(forms.ModelForm):
    """Form for creating/editing company courses"""

    class Meta:
        model = CompanyCourse
        fields = [
            'name', 'description', 'is_active', 'is_public',
            'teams', 'start_role', 'max_role'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'teams': forms.TextInput(attrs={'placeholder': 'Engineering, Marketing, Sales'}),
        }


class CourseTaskForm(forms.ModelForm):
    """Form for creating/editing course tasks"""

    class Meta:
        model = CourseTask
        fields = [
            'title', 'description', 'role', 'task_id',
            'order', 'challenge_text', 'success_criteria'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 2}),
            'challenge_text': forms.Textarea(attrs={'rows': 5}),
            'success_criteria': forms.Textarea(attrs={'rows': 3}),
        }


class TeamInvitationForm(forms.Form):
    """Form for inviting team members"""
    emails = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'placeholder': 'Enter email addresses, one per line'}),
        help_text="Enter one email address per line"
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False,
        help_text="Optional message to include in the invitation"
    )

    def clean_emails(self):
        emails_text = self.cleaned_data['emails']
        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]

        # Validate email format
        invalid_emails = []
        for email in emails:
            if not forms.EmailField().clean(email):
                invalid_emails.append(email)

        if invalid_emails:
            raise ValidationError(f"Invalid email addresses: {', '.join(invalid_emails)}")

        return emails
