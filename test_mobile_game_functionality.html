<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Mobile Game Functionality Test - Corporate Prompt Master</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #121212;
            color: #ffffff;
        }
        
        .test-container {
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #444444;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #5a67d8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin: 8px 0;
            min-height: 44px;
        }
        
        .test-button:active {
            transform: scale(0.95);
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 8px 0;
            font-size: 14px;
        }
        
        .status.success {
            background: #4facfe;
            color: white;
        }
        
        .status.error {
            background: #ff9a9e;
            color: white;
        }
        
        .status.info {
            background: #f0f2f5;
            color: #333;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #444;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checklist li:last-child {
            border-bottom: none;
        }
        
        .check-box {
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .check-box.checked {
            background: #667eea;
            color: white;
        }
        
        .game-flow {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .flow-step {
            margin: 4px 0;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .flow-step.user {
            background: #667eea;
            color: white;
        }
        
        .flow-step.system {
            background: #4facfe;
            color: white;
        }
        
        .flow-step.result {
            background: #00f2fe;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1>Mobile Game Functionality Test</h1>
            <p>This page tests the complete mobile game flow including prompt submission, preview, and game progression.</p>
        </div>
        
        <div class="test-section">
            <h2>🎮 Complete Game Flow Test</h2>
            <p>Test the full mobile game experience:</p>
            
            <div class="game-flow">
                <div class="flow-step user">1. User enters prompt in mobile interface</div>
                <div class="flow-step system">2. Mobile syncs with desktop game logic</div>
                <div class="flow-step system">3. Preview button generates AI response</div>
                <div class="flow-step system">4. Submit button appears after preview</div>
                <div class="flow-step user">5. User submits final response</div>
                <div class="flow-step result">6. Game progresses and updates stats</div>
            </div>
            
            <button class="test-button" onclick="testGameFlow()">Test Complete Game Flow</button>
            <div id="game-flow-result" class="status info">Click button to test game flow</div>
        </div>
        
        <div class="test-section">
            <h2>✅ Mobile Game Functionality Checklist</h2>
            <ul class="checklist">
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Mobile interface loads correctly</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Hamburger menu opens and scrolls smoothly</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Prompt input accepts text and syncs</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Preview button generates AI response</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Submit button appears after preview</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Submit button submits response</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Game stats update after submission</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Messages appear in mobile interface</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Loading states show during operations</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Toast notifications work correctly</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Game progression works like desktop</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>All backend functionality preserved</span>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🔧 Integration Test</h2>
            <p>Test mobile-desktop integration:</p>
            <button class="test-button" onclick="testIntegration()">Test Desktop Integration</button>
            <div id="integration-result" class="status info">Test mobile-desktop sync</div>
        </div>
        
        <div class="test-section">
            <h2>📱 Mobile-Specific Features</h2>
            <ul class="checklist">
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Touch-friendly 44px minimum targets</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Swipe gestures work (swipe right for menu)</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Keyboard shortcuts work (Alt+L, Escape)</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Virtual keyboard doesn't break layout</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Orientation changes work smoothly</span>
                </li>
                <li>
                    <div class="check-box" onclick="toggleCheck(this)"></div>
                    <span>Performance is smooth (60fps)</span>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🎯 Game Progression Test</h2>
            <p>Test that mobile mode progresses the game correctly:</p>
            
            <div class="game-flow">
                <div class="flow-step user">Expected: Enter "Write a professional cover letter"</div>
                <div class="flow-step system">Expected: AI generates cover letter preview</div>
                <div class="flow-step user">Expected: Submit the response</div>
                <div class="flow-step result">Expected: Score increases, challenge count increases</div>
                <div class="flow-step result">Expected: New challenge appears or role advances</div>
            </div>
            
            <button class="test-button" onclick="window.open('/game/', '_blank')">Open Game in New Tab</button>
            <div class="status info">Test the complete game flow in the actual game interface</div>
        </div>
        
        <div class="test-section">
            <h2>🚨 Known Issues to Check</h2>
            <ul style="color: #ff9a9e; font-size: 14px;">
                <li>Ensure mobile preview calls correct backend API</li>
                <li>Verify mobile submit triggers game progression</li>
                <li>Check that game state updates in mobile UI</li>
                <li>Confirm messages sync between mobile and desktop</li>
                <li>Test that loading states work correctly</li>
                <li>Verify error handling for failed requests</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Results Summary</h2>
            <div id="test-summary" class="status info">
                Complete the checklist above to see your test results summary.
            </div>
            <button class="test-button" onclick="generateSummary()">Generate Test Summary</button>
        </div>
    </div>

    <script>
        function toggleCheck(element) {
            element.classList.toggle('checked');
            if (element.classList.contains('checked')) {
                element.innerHTML = '✓';
            } else {
                element.innerHTML = '';
            }
            updateSummary();
        }
        
        function updateSummary() {
            const total = document.querySelectorAll('.check-box').length;
            const checked = document.querySelectorAll('.check-box.checked').length;
            const percentage = Math.round((checked / total) * 100);
            
            const summary = document.getElementById('test-summary');
            if (percentage === 100) {
                summary.className = 'status success';
                summary.textContent = `✅ Perfect! All ${total} tests passed (${percentage}%)`;
            } else if (percentage >= 80) {
                summary.className = 'status info';
                summary.textContent = `⚠️ Good progress: ${checked}/${total} tests passed (${percentage}%)`;
            } else {
                summary.className = 'status error';
                summary.textContent = `❌ Needs work: ${checked}/${total} tests passed (${percentage}%)`;
            }
        }
        
        function testGameFlow() {
            const result = document.getElementById('game-flow-result');
            result.className = 'status info';
            result.textContent = '🔄 Testing game flow... Open the game and follow the steps above.';
            
            setTimeout(() => {
                result.className = 'status success';
                result.textContent = '✅ Game flow test completed. Check the actual game for results.';
            }, 2000);
        }
        
        function testIntegration() {
            const result = document.getElementById('integration-result');
            result.className = 'status info';
            result.textContent = '🔄 Testing mobile-desktop integration...';
            
            // Simulate integration test
            setTimeout(() => {
                const hasDesktopElements = document.querySelector('.desktop-interface') !== null;
                const hasMobileElements = document.querySelector('.mobile-interface') !== null;
                
                if (hasDesktopElements || hasMobileElements) {
                    result.className = 'status success';
                    result.textContent = '✅ Integration test passed. Mobile and desktop elements detected.';
                } else {
                    result.className = 'status error';
                    result.textContent = '❌ Integration test failed. Game elements not found.';
                }
            }, 1500);
        }
        
        function generateSummary() {
            updateSummary();
            const summary = document.getElementById('test-summary');
            const total = document.querySelectorAll('.check-box').length;
            const checked = document.querySelectorAll('.check-box.checked').length;
            
            console.log('Mobile Game Functionality Test Results:');
            console.log(`Total tests: ${total}`);
            console.log(`Passed tests: ${checked}`);
            console.log(`Success rate: ${Math.round((checked / total) * 100)}%`);
            
            // Add detailed breakdown
            const checkedItems = Array.from(document.querySelectorAll('.check-box.checked'))
                .map(box => box.nextElementSibling.textContent);
            const uncheckedItems = Array.from(document.querySelectorAll('.check-box:not(.checked)'))
                .map(box => box.nextElementSibling.textContent);
                
            console.log('Passed tests:', checkedItems);
            console.log('Failed tests:', uncheckedItems);
        }
        
        // Initialize
        updateSummary();
        
        console.log('Mobile Game Functionality Test Page Loaded');
        console.log('Instructions:');
        console.log('1. Open /game/ in mobile view');
        console.log('2. Test each functionality item');
        console.log('3. Check off completed items');
        console.log('4. Generate summary when done');
    </script>
</body>
</html>
