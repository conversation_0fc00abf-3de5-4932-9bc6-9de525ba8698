"""
Organization Chart Generator for Rwenzori Innovations Game

This module provides functions to generate HTML for the organization chart.
"""
import logging
import json

# Configure logging
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('org_chart_generator')

def generate_org_chart_html(current_role, completed_roles):
    """
    Generate HTML for the organization chart, highlighting the player's current position.

    Args:
        current_role: The current role of the player
        completed_roles: List of roles the player has completed

    Returns:
        HTML string for the organization chart
    """
    # Debug logging
    logger.debug(f"Generating org chart HTML with current_role={current_role}, completed_roles={completed_roles}")

    # Validate inputs
    if not current_role:
        logger.warning("No current_role provided, defaulting to 'applicant'")
        current_role = 'applicant'

    if completed_roles is None:
        logger.warning("No completed_roles provided, defaulting to empty list")
        completed_roles = []

    try:
        # Convert to string for logging if it's not already
        if not isinstance(completed_roles, list):
            logger.error(f"completed_roles is not a list: {type(completed_roles)}")
            completed_roles = []

        logger.debug(f"Using current_role={current_role}, completed_roles={json.dumps(completed_roles)}")
    except Exception as e:
        logger.error(f"Error processing inputs: {e}")

    # Define the organizational structure for the chart
    org_structure = [
        # Executive Level
        {
            "level": "Executive",
            "roles": ["shareholders", "ceo", "coo"]
        },
        # VP Level
        {
            "level": "Vice Presidents",
            "roles": ["vp_marketing", "vp_operations", "vp_finance", "hr_director"]
        },
        # Manager Level
        {
            "level": "Managers",
            "roles": [
                "advertising_manager", "sales_manager",  # Marketing
                "service_manager", "production_manager", "facilities_manager",  # Operations
                "accounts_receivable_manager", "accounts_payable_manager",  # Finance
                "hr_manager"  # HR Department
            ]
        },
        # Associate Level
        {
            "level": "Associates",
            "roles": [
                "sales_associate", "marketing_associate",  # Marketing
                "service_associate", "production_associate", "facilities_associate",  # Operations
                "accounts_receivable_associate", "accounts_payable_associate",  # Finance
                "hr_coordinator"  # HR Department
            ]
        },
        # Entry Level
        {
            "level": "Entry",
            "roles": ["junior_assistant", "applicant"]
        }
    ]

    # Role titles for display
    role_titles = {
        "shareholders": "Shareholders",
        "ceo": "Chief Executive Officer",
        "coo": "Chief Operating Officer",
        "vp_marketing": "VP Marketing",
        "vp_operations": "VP Operations",
        "vp_finance": "VP Finance",
        "hr_director": "HR Director",
        "advertising_manager": "Advertising Manager",
        "sales_manager": "Sales Manager",
        "service_manager": "Service Manager",
        "production_manager": "Production Manager",
        "facilities_manager": "Facilities Manager",
        "accounts_receivable_manager": "AR Manager",
        "accounts_payable_manager": "AP Manager",
        "hr_manager": "HR Manager",
        "sales_associate": "Sales Associate",
        "marketing_associate": "Marketing Associate",
        "service_associate": "Service Associate",
        "production_associate": "Production Associate",
        "facilities_associate": "Facilities Associate",
        "accounts_receivable_associate": "AR Associate",
        "accounts_payable_associate": "AP Associate",
        "hr_coordinator": "HR Coordinator",
        "junior_assistant": "Junior Assistant",
        "applicant": "Applicant"
    }

    # Start building the HTML
    html = '<div class="org-chart-content">'

    # Add each level
    for level in org_structure:
        html += f'<div class="org-level"><div class="level-title">{level["level"]}</div><div class="level-roles">'

        # Add roles for this level
        for role in level["roles"]:
            # Determine role status
            if role == current_role:
                status = "current"
                print(f"Marking role {role} as current in org chart")
            elif role in completed_roles:
                status = "completed"
            else:
                status = "future"

            # Get role title
            title = role_titles.get(role, role.replace("_", " ").title())

            # Add role node with enhanced styling for current role
            if status == "current":
                html += f'<div class="org-node {status}" title="{title}">'
                html += f'<div class="node-indicator">{get_status_indicator(status)}</div>'
                html += f'<div class="node-title"><strong>{title}</strong></div>'
                html += f'<div class="current-marker">YOU ARE HERE</div>'
                html += '</div>'
            else:
                html += f'<div class="org-node {status}" title="{title}">'
                html += f'<div class="node-indicator">{get_status_indicator(status)}</div>'
                html += f'<div class="node-title">{title}</div>'
                html += '</div>'

        html += '</div></div>'

    html += '</div>'

    # Add debug information as a hidden comment in the HTML
    html += f'<!-- DEBUG INFO: current_role={current_role}, completed_roles={json.dumps(completed_roles)} -->'

    # Log the generated HTML (truncated for readability)
    logger.debug(f"Generated org chart HTML (first 200 chars): {html[:200]}...")

    return html

def get_status_indicator(status):
    """Return the appropriate status indicator based on role status."""
    if status == "completed":
        return "✓"
    elif status == "current":
        return "➤"
    else:
        return "○"
