# Generated by Django 5.2 on 2025-05-13 10:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corporate', '0009_companymembership'),
        ('game', '0007_alter_leaderboardentry_unique_together_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='gamesession',
            name='company',
            field=models.ForeignKey(blank=True, help_text='The company this game session belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='game_sessions', to='corporate.company'),
        ),
        migrations.AlterField(
            model_name='companygamesettings',
            name='company',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='game_settings', to='corporate.company'),
        ),
        migrations.RemoveField(
            model_name='companyleaderboard',
            name='company',
        ),
        migrations.AlterField(
            model_name='companycourse',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_courses', to='corporate.company'),
        ),
        migrations.RemoveField(
            model_name='leaderboardentry',
            name='leaderboard',
        ),
        migrations.AlterUniqueTogether(
            name='leaderboardentry',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='leaderboardentry',
            name='user',
        ),
        migrations.DeleteModel(
            name='Company',
        ),
        migrations.DeleteModel(
            name='CompanyLeaderboard',
        ),
        migrations.DeleteModel(
            name='LeaderboardEntry',
        ),
    ]
