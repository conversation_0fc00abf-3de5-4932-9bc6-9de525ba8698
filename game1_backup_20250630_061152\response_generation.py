"""
Response generation for the Context-Aware Game.
This module contains functions for generating responses.
"""

import os
import logging
from .utils import log_info, log_error

# Check if LLM response generator is available
try:
    from llm_response_generator_openai_format import generate_response_with_llm
    LLM_RESPONSE_GENERATOR_AVAILABLE = True
    log_info("LLM response generator module loaded successfully")
except ImportError as e:
    log_error("LLM response generator module not available", e)
    LLM_RESPONSE_GENERATOR_AVAILABLE = False

# Check if LLM feedback generator is available
try:
    from llm_feedback_generator import generate_manager_feedback
    LLM_FEEDBACK_GENERATOR_AVAILABLE = True
    log_info("LLM feedback generator module loaded successfully")
except ImportError as e:
    log_error("LLM feedback generator module not available", e)
    LLM_FEEDBACK_GENERATOR_AVAILABLE = False

def generate_ai_response(prompt, task):
    """
    Generate an AI response for the given prompt and task.
    
    Args:
        prompt (str): The user's prompt
        task (dict): The current task
        
    Returns:
        str: The generated AI response
    """
    task_id = task["id"]
    
    # Determine whether to use LLM for response generation
    use_llm_response = LLM_RESPONSE_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_RESPONSES", "false").lower() == "true"
    # Force use_llm_response to True for testing
    use_llm_response = True
    log_info(f"Using LLM for response generation: {use_llm_response}")
    
    # Generate response using LLM or template
    if use_llm_response:
        try:
            # Generate response using LLM
            log_info(f"Attempting to generate response using LLM for task {task_id} with prompt: '{prompt[:50]}...'")
            
            # Call the LLM response generator
            ai_response = generate_response_with_llm(prompt, task_id)
            
            # Log the response
            log_info(f"Successfully generated response using LLM for task {task_id}. Response preview: '{ai_response[:50]}...'")
            
            # Make sure we're not using the template response
            if ai_response == task["response_template"]:
                log_info("LLM response matches template response exactly. This might indicate the LLM response is not being used.")
                # Force a different response to ensure we're not using the template
                ai_response = "This is a forced response to ensure we're not using the template. " + ai_response
        except Exception as e:
            # Fall back to template if LLM fails
            log_error(f"Error generating response with LLM", e)
            ai_response = task["response_template"]
            log_info(f"Falling back to template response for task {task_id}")
    else:
        # Use template response
        log_info(f"Using template response for task {task_id} (LLM response generation is disabled)")
        ai_response = task["response_template"]
    
    return ai_response

def generate_feedback(prompt, task, performance_metrics, characters):
    """
    Generate feedback for the user's prompt.
    
    Args:
        prompt (str): The user's prompt
        task (dict): The current task
        performance_metrics (dict): The performance metrics
        characters (dict): The characters
        
    Returns:
        str: The generated feedback
    """
    # Get the feedback grade
    feedback_grade = performance_metrics.get("grade", "bad")
    
    # Generate personalized feedback using LLM if available
    if LLM_FEEDBACK_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_FEEDBACK", "true").lower() == "true":
        try:
            # Get the manager character
            manager_character = characters.get(task["manager"], {})
            manager_name = manager_character.get("name", task["manager"])
            
            # Format the performance score
            formatted_performance_score = f"{performance_metrics.get('overall_score', 0):.1f}"
            performance_metrics["formatted_score"] = formatted_performance_score
            
            # Generate feedback
            feedback_text = generate_manager_feedback(
                prompt,
                task["id"],
                manager_name,
                performance_metrics
            )
            log_info(f"Generated personalized feedback for task {task['id']}")
        except Exception as e:
            log_error(f"Error generating personalized feedback", e)
            feedback_text = task["feedback"].get(feedback_grade, "Could not generate personalized feedback.")
    else:
        # Use template feedback
        feedback_text = task["feedback"].get(feedback_grade, "Feedback based on your performance.")
    
    return feedback_text

def preview_response(prompt, task_id):
    """
    Generate a preview response for the given prompt and task.
    
    Args:
        prompt (str): The user's prompt
        task_id (str): The ID of the task
        
    Returns:
        str: The generated preview response
    """
    try:
        # Generate response using LLM
        log_info(f"Generating LLM response for preview with prompt: '{prompt[:50]}...'")
        
        # Call the LLM response generator
        preview_response = generate_response_with_llm(prompt, task_id)
        
        # Log the preview response
        log_info(f"Generated preview response for task {task_id}. Response preview: '{preview_response[:50]}...'")
        
        return preview_response
    except Exception as e:
        log_error(f"Error generating preview response", e)
        return "Error generating preview response. Please try again."
