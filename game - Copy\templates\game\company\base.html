{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Company Dashboard{% endblock %} - Corporate Prompt Master</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicons/favicon.ico' %}">
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicons/favicon.svg' %}">
    <link rel="apple-touch-icon" href="{% static 'img/favicons/favicon.ico' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <style>
        :root {
            --bs-primary: #0d6efd;
            {% if company and company.game_settings.use_company_branding and company.game_settings.primary_color %}
            --bs-primary: {{ company.game_settings.primary_color }};
            {% endif %}
        }

        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .sidebar {
            min-width: 250px;
            background-color: #212529;
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: var(--bs-primary);
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }

        .content {
            flex: 1;
            padding: 1.5rem;
        }

        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: 500;
        }

        .stats-card {
            text-align: center;
            padding: 1rem;
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--bs-primary);
        }

        .stats-card .value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .stats-card .label {
            color: #6c757d;
        }

        footer {
            padding: 1rem;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }

        /* Dark mode */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #212529;
                color: #f8f9fa;
            }

            .card {
                background-color: #2c3034;
                border-color: #373b3e;
            }

            .card-header {
                background-color: #2c3034;
                border-color: #373b3e;
            }

            footer {
                background-color: #212529;
                border-color: #373b3e;
                color: #adb5bd;
            }
        }
    </style>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    {% block extra_css %}{% endblock %}
</head>
<body class="{% if request.COOKIES.theme == 'dark' or request.META.HTTP_SEC_CH_PREFERS_COLOR_SCHEME == 'dark' %}dark-mode{% endif %}">
    <!-- Unified Navigation Header -->
    {% include 'unified_header.html' %}

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if company %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="px-3 py-2 mb-3">
                        <h5>{{ company.name }}</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'company_dashboard' %}active{% endif %}"
                               href="{% url 'game:company_dashboard' company_slug=company.slug %}">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'company_team' %}active{% endif %}"
                               href="{% url 'game:company_team' company_slug=company.slug %}">
                                <i class="bi bi-people"></i> Team
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'company_courses' or request.resolver_match.url_name == 'company_course_detail' %}active{% endif %}"
                               href="{% url 'game:company_courses' company_slug=company.slug %}">
                                <i class="bi bi-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'company_leaderboard' %}active{% endif %}"
                               href="{% url 'game:company_leaderboard' company_slug=company.slug %}">
                                <i class="bi bi-trophy"></i> Leaderboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'company_settings' %}active{% endif %}"
                               href="{% url 'game:company_settings' company_slug=company.slug %}">
                                <i class="bi bi-gear"></i> Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Content -->
            <main class="{% if company %}col-md-9 col-lg-10{% else %}col-12{% endif %} content">
                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-auto">
        <div class="container">
            <p class="mb-0">&copy; {% now "Y" %} Corporate Prompt Master. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
