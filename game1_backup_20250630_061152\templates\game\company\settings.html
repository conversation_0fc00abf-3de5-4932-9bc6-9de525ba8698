{% extends 'game/company/base.html' %}

{% block title %}Settings - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="mb-4">
        <h1 class="h2">Company Settings</h1>
        <p class="text-muted">Configure game settings for your company</p>
    </div>
    
    <!-- Settings Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Game Settings</h5>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <h6 class="mb-3">Access Settings</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            {{ form.is_public }}
                            <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                {{ form.is_public.label }}
                            </label>
                            {% if form.is_public.help_text %}
                            <div class="form-text">{{ form.is_public.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            {{ form.require_login }}
                            <label class="form-check-label" for="{{ form.require_login.id_for_label }}">
                                {{ form.require_login.label }}
                            </label>
                            {% if form.require_login.help_text %}
                            <div class="form-text">{{ form.require_login.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Leaderboard Settings</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            {{ form.show_leaderboard }}
                            <label class="form-check-label" for="{{ form.show_leaderboard.id_for_label }}">
                                {{ form.show_leaderboard.label }}
                            </label>
                            {% if form.show_leaderboard.help_text %}
                            <div class="form-text">{{ form.show_leaderboard.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            {{ form.show_in_global_leaderboard }}
                            <label class="form-check-label" for="{{ form.show_in_global_leaderboard.id_for_label }}">
                                {{ form.show_in_global_leaderboard.label }}
                            </label>
                            {% if form.show_in_global_leaderboard.help_text %}
                            <div class="form-text">{{ form.show_in_global_leaderboard.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Custom Messages</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.custom_welcome_message.id_for_label }}" class="form-label">
                                {{ form.custom_welcome_message.label }}
                            </label>
                            {{ form.custom_welcome_message }}
                            {% if form.custom_welcome_message.help_text %}
                            <div class="form-text">{{ form.custom_welcome_message.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.custom_completion_message.id_for_label }}" class="form-label">
                                {{ form.custom_completion_message.label }}
                            </label>
                            {{ form.custom_completion_message }}
                            {% if form.custom_completion_message.help_text %}
                            <div class="form-text">{{ form.custom_completion_message.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Branding</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            {{ form.use_company_branding }}
                            <label class="form-check-label" for="{{ form.use_company_branding.id_for_label }}">
                                {{ form.use_company_branding.label }}
                            </label>
                            {% if form.use_company_branding.help_text %}
                            <div class="form-text">{{ form.use_company_branding.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.primary_color.id_for_label }}" class="form-label">
                                {{ form.primary_color.label }}
                            </label>
                            {{ form.primary_color }}
                            {% if form.primary_color.help_text %}
                            <div class="form-text">{{ form.primary_color.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Danger Zone -->
    <div class="card mt-4 border-danger">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">Danger Zone</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6>Delete Company</h6>
                    <p class="mb-0 text-muted">Once you delete a company, there is no going back. Please be certain.</p>
                </div>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCompanyModal">
                    Delete Company
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Company Modal -->
<div class="modal fade" id="deleteCompanyModal" tabindex="-1" aria-labelledby="deleteCompanyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCompanyModalLabel">Delete Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ company.name }}</strong>? This action cannot be undone.</p>
                <p>All game sessions, courses, and team data associated with this company will be permanently deleted.</p>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill"></i> This action is irreversible!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#">
                    {% csrf_token %}
                    <input type="hidden" name="delete_company" value="1">
                    <button type="submit" class="btn btn-danger">Delete Company</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize form elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(function(textarea) {
            textarea.classList.add('form-control');
        });
        
        const colorInput = document.getElementById('{{ form.primary_color.id_for_label }}');
        if (colorInput) {
            colorInput.classList.add('form-control', 'form-control-color');
        }
        
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(function(checkbox) {
            checkbox.classList.add('form-check-input');
        });
    });
</script>
{% endblock %}
