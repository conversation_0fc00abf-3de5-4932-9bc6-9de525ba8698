from django.contrib import admin
from .models import GameSession, Message, CompanyGameSettings, AnonymousPlayerSettings

class MessageInline(admin.TabularInline):
    model = Message
    extra = 0
    readonly_fields = ('message_id', 'sender', 'text', 'html', 'timestamp', 'is_challenge', 'is_markdown', 'task_id')

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

def delete_all_messages(modeladmin, request, queryset):
    """Admin action to delete all messages for selected game sessions"""
    import logging
    for game_session in queryset:
        message_count = game_session.messages.count()
        logging.info(f"Admin action: Deleting {message_count} messages for game session {game_session.id}")

        # Use raw SQL to ensure messages are deleted
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM game_message WHERE game_session_id = %s", [game_session.id])
            rows_deleted = cursor.rowcount
            logging.info(f"SQL DELETE affected {rows_deleted} rows")

        # Verify deletion
        message_count_after = game_session.messages.count()
        logging.info(f"After deletion: {message_count_after} messages remain for game session {game_session.id}")

delete_all_messages.short_description = "Delete all messages for selected game sessions"

@admin.register(GameSession)
class GameSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'session_id', 'current_role', 'performance_score', 'challenges_completed', 'created_at')
    list_filter = ('current_role', 'game_completed')
    search_fields = ('user__username', 'session_id')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [MessageInline]
    actions = [delete_all_messages]

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'game_session', 'sender', 'timestamp', 'is_challenge', 'task_id')
    list_filter = ('sender', 'is_challenge', 'is_markdown')
    search_fields = ('text', 'sender', 'task_id')
    readonly_fields = ('timestamp',)

@admin.register(CompanyGameSettings)
class CompanyGameSettingsAdmin(admin.ModelAdmin):
    list_display = ('company', 'is_public', 'require_login', 'show_leaderboard', 'help_url')
    list_filter = ('is_public', 'require_login', 'show_leaderboard', 'show_in_global_leaderboard')
    search_fields = ('company__name', 'help_url')
    fieldsets = (
        ('Company Information', {
            'fields': ('company',)
        }),
        ('Game Visibility', {
            'fields': ('is_public', 'require_login')
        }),
        ('Leaderboard Settings', {
            'fields': ('show_leaderboard', 'show_in_global_leaderboard')
        }),
        ('Custom Messages', {
            'fields': ('custom_welcome_message', 'custom_completion_message')
        }),
        ('Help Settings', {
            'fields': ('help_url',),
            'description': 'Configure the URL for the help button in the game header'
        }),
        ('Branding', {
            'fields': ('use_company_branding', 'primary_color')
        }),
    )


@admin.register(AnonymousPlayerSettings)
class AnonymousPlayerSettingsAdmin(admin.ModelAdmin):
    list_display = ('cleanup_enabled', 'cleanup_hours', 'last_cleanup')
    fieldsets = (
        ('Cleanup Settings', {
            'fields': ('cleanup_enabled', 'cleanup_hours'),
            'description': 'Configure automatic cleanup of anonymous player data'
        }),
        ('Status', {
            'fields': ('last_cleanup',),
            'description': 'Last time anonymous player data was cleaned up'
        }),
    )

    def has_add_permission(self, request):
        # Only allow adding if no settings exist yet
        return AnonymousPlayerSettings.objects.count() == 0

    def has_delete_permission(self, request, obj=None):
        # Don't allow deleting the settings
        return False
