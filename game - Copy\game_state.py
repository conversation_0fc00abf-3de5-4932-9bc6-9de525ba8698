"""
Game state management for the Context-Aware Game.
This module contains functions for managing the game state.
"""

import logging
import markdown
from .utils import create_message, log_info, log_error
from .role_progression import get_next_role, get_promotion_message, ROLE_PROGRESSION
from .characters import CHARACTERS
from .all_role_tasks import get_all_role_tasks

# Initialize the game state
def initialize_game_state():
    """
    Initialize a new game state.

    Returns:
        dict: The initialized game state
    """
    game_state = {
        "current_role": "applicant",
        "performance_score": 0,
        "challenges_completed": 0,
        "role_challenges_completed": 0,  # Track challenges completed in current role
        "messages": [],
        "game_completed": False,
        "current_manager": "hr",
        "current_task": "cover_letter",
        "completed_roles": [],
        "first_task_pending": True  # Flag to indicate first task should be delayed
    }

    log_info(f"Game state initialized with performance_score: {game_state['performance_score']}")
    log_info(f"Game state: {game_state}")

    return game_state

# DEPRECATED: This function is no longer used - replaced by game_state_manager.process_task_completion
# Keeping for reference but renamed to avoid conflicts
def process_task_completion_deprecated(game_state, evaluation_results, points_earned):
    """
    DEPRECATED: Process the completion of a task.
    This function is no longer used - the active implementation is in game_state_manager.py

    Args:
        game_state (dict): The current game state
        evaluation_results (dict): The evaluation results
        points_earned (int): The points earned for this task

    Returns:
        dict: The updated game state
    """
    log_info(f"DEPRECATED FUNCTION CALLED - Processing task completion. Evaluation results: {evaluation_results}, Points earned: {points_earned}")

    # Update the performance score
    game_state["performance_score"] = game_state.get("performance_score", 0) + points_earned

    # Check if the player has completed all challenges for the current role
    current_role = game_state["current_role"]
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)

    log_info(f"Current role: {current_role}, Challenges completed: {game_state['role_challenges_completed']}, Required: {challenges_required}")

    # Check if the player has completed all challenges for the current role
    if game_state["role_challenges_completed"] >= challenges_required:
        # Check if there's a next role to promote to
        next_role = get_next_role(current_role)

        if next_role:
            # Add current role to completed roles
            if current_role not in game_state["completed_roles"]:
                game_state["completed_roles"].append(current_role)

            # Update game state with new role
            game_state["current_role"] = next_role
            game_state["role_challenges_completed"] = 0

            log_info(f"Player promoted from {current_role} to {next_role}")

            # Get the first task for the new role
            new_role_tasks = get_all_role_tasks(next_role)
            if new_role_tasks:
                next_task = new_role_tasks[0]
                game_state["current_task"] = next_task["id"]
                game_state["current_manager"] = next_task["manager"]

                log_info(f"Next task set to {next_task['id']} with manager {next_task['manager']}")
            else:
                log_error(f"No tasks found for role {next_role}")
                game_state["game_completed"] = True
                game_state["current_task"] = None
        else:
            # No next role, game completed
            log_info(f"No next role after {current_role}. Game completed!")
            game_state["game_completed"] = True
            game_state["current_task"] = None

    return game_state

# Process task failure
def process_task_failure(game_state):
    """
    Process the failure of a task.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: The updated game state
    """
    log_info("Processing task failure")

    # The player stays in the current role and keeps the current task
    # No changes to role_challenges_completed or challenges_completed

    return game_state

# Check for promotion
def check_for_promotion(game_state):
    """
    Check if the player should be promoted to the next role.

    Args:
        game_state (dict): The current game state

    Returns:
        tuple: (bool, str) - Whether the player should be promoted and the next role
    """
    current_role = game_state["current_role"]
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)
    next_role = role_info.get("next_role")

    # Check if the player has completed all challenges for the current role
    if game_state["role_challenges_completed"] >= challenges_required and next_role:
        return True, next_role

    return False, None

# Get performance summary
def get_performance_summary(game_state):
    """
    Get a summary of the player's performance.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: A summary of the player's performance
    """
    return {
        "performance_score": game_state["performance_score"],
        "challenges_completed": game_state["challenges_completed"],
        "role_challenges_completed": game_state["role_challenges_completed"],
        "current_role": game_state["current_role"],
        "completed_roles": game_state["completed_roles"]
    }

# Add a message to the game state
def add_message_to_game_state(game_state, message):
    """
    Add a message to the game state.

    Args:
        game_state (dict): The current game state
        message (dict): The message to add

    Returns:
        dict: The updated game state
    """
    game_state["messages"].append(message)
    return game_state

# Add a welcome message to the game state
def add_welcome_message(game_state):
    """
    Add a welcome message to the game state.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: The updated game state
    """
    welcome_text = f'''## Welcome to Rwenzori Innovations! 🎉

I'm **{CHARACTERS['hr']['name']}**, the {CHARACTERS['hr']['title']}.

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.'''

    welcome_message = create_message("hr", welcome_text)
    return add_message_to_game_state(game_state, welcome_message)

# Add a completion message to the game state
def add_completion_message(game_state):
    """
    Add a completion message to the game state.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: The updated game state
    """
    completion_text = '''## Congratulations! 🎉

You've completed all the challenges and proven your prompt engineering skills! You've successfully progressed through all departments of Rwenzori Innovations:

### Entry Level
- Started as an **Applicant** in HR
- Became a **Junior Assistant** in HR

### Marketing Department
- Advanced to **Sales Associate** in Marketing
- Promoted to **Marketing Associate** in Marketing
- Became a **Sales Manager** in Marketing
- Moved up to **Advertising/Research Manager** in Marketing
- Reached **Vice-President of Marketing**

### Operations Department
- Transferred to **Service Associate** in Operations
- Moved to **Production Associate** in Operations
- Advanced to **Facilities Associate** in Operations
- Promoted to **Service Manager** in Operations
- Became a **Production Manager** in Operations
- Moved up to **Facilities Manager** in Operations
- Reached **Vice-President of Operations**

### Finance Department
- Transferred to **Accounts Receivable Associate** in Finance
- Moved to **Accounts Payable Associate** in Finance
- Promoted to **Accounts Receivable Manager** in Finance
- Advanced to **Accounts Payable Manager** in Finance
- Reached **Vice-President of Finance**

### HR Department
- Transferred to **HR Coordinator** in Human Resources
- Promoted to **HR Manager** in Human Resources
- Advanced to **HR Director** in Human Resources

### Executive Level
- Promoted to **Chief Operating Officer (COO)**
- Advanced to **Chief Executive Officer (CEO)**
- And finally joined the **Shareholders** at the very top of Rwenzori Innovations!

Thank you for demonstrating your exceptional abilities in crafting effective prompts and managing AI interactions across all levels of the organization.'''

    completion_message = create_message("ceo", completion_text)
    return add_message_to_game_state(game_state, completion_message)
