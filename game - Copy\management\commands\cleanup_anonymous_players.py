import logging
from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from game.models import GameSession, AnonymousPlayerSettings

class Command(BaseCommand):
    help = 'Clean up anonymous player data after a configurable time period'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force cleanup regardless of the time since last cleanup',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--hours',
            type=int,
            help='Override the number of hours from settings',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        dry_run = options.get('dry-run', False)
        hours_override = options.get('hours')
        
        # Get or create settings
        settings, created = AnonymousPlayerSettings.objects.get_or_create(
            pk=1,
            defaults={
                'cleanup_enabled': True,
                'cleanup_hours': 24,
                'last_cleanup': None
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created default anonymous player settings'))
        
        # Check if cleanup is enabled
        if not settings.cleanup_enabled and not force:
            self.stdout.write(self.style.WARNING('Anonymous player cleanup is disabled. Use --force to run anyway.'))
            return
        
        # Use override hours if provided
        hours = hours_override if hours_override is not None else settings.cleanup_hours
        
        # Calculate cutoff time
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        # Find anonymous sessions older than the cutoff time
        anonymous_sessions = GameSession.objects.filter(
            user__isnull=True,
            session_id__isnull=False,
            updated_at__lt=cutoff_time
        )
        
        count = anonymous_sessions.count()
        self.stdout.write(f"Found {count} anonymous game sessions older than {hours} hours")
        
        if count > 0:
            if not dry_run:
                # Delete the sessions (this will cascade to messages due to the foreign key relationship)
                deleted_count, details = anonymous_sessions.delete()
                
                # Update last cleanup timestamp
                settings.last_cleanup = timezone.now()
                settings.save()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Deleted {deleted_count} anonymous game sessions and related data"
                    )
                )
                
                # Log details of what was deleted
                for model, count in details.items():
                    self.stdout.write(f"  - {count} {model} objects deleted")
                
                # Log to the application log as well
                logging.info(f"Cleaned up {deleted_count} anonymous game sessions older than {hours} hours")
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Dry run - would have deleted {count} anonymous game sessions"
                    )
                )
                
                # Show some details about what would be deleted
                for session in anonymous_sessions[:5]:  # Show details for up to 5 sessions
                    message_count = session.messages.count()
                    self.stdout.write(
                        f"  - Session {session.id} (created {session.created_at}, "
                        f"updated {session.updated_at}) with {message_count} messages"
                    )
                
                if count > 5:
                    self.stdout.write(f"  - ... and {count - 5} more sessions")
        else:
            self.stdout.write(self.style.SUCCESS("No anonymous sessions to clean up"))
